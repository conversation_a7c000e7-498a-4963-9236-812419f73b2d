"""
Google Gemini API client wrapper for OpenAI Agents SDK compatibility
"""
import google.generativeai as genai
from typing import Dict, List, Any, Optional, AsyncGenerator
import async<PERSON>
import json
from config import Config

class GeminiClient:
    """Wrapper class to make Gemini API compatible with OpenAI Agents SDK"""
    
    def __init__(self, api_key: str = None, model: str = None):
        self.api_key = api_key or Config.GEMINI_API_KEY
        self.model_name = model or Config.GEMINI_MODEL
        
        # Configure Gemini
        genai.configure(api_key=self.api_key)
        self.model = genai.GenerativeModel(self.model_name)
        
    async def create_completion(
        self,
        messages: List[Dict[str, str]],
        temperature: float = None,
        max_tokens: int = None,
        tools: List[Dict] = None,
        **kwargs
    ) -> Dict[str, Any]:
        """
        Create a completion using Gemini API with OpenAI-style interface
        """
        try:
            # Convert OpenAI-style messages to Gemini format
            gemini_messages = self._convert_messages(messages)
            
            # Set generation config
            generation_config = genai.types.GenerationConfig(
                temperature=temperature or Config.GEMINI_TEMPERATURE,
                max_output_tokens=max_tokens or Config.GEMINI_MAX_TOKENS,
            )
            
            # Handle tools if provided
            if tools:
                # Convert OpenAI-style tools to Gemini format
                gemini_tools = self._convert_tools(tools)
                response = await self._generate_with_tools(
                    gemini_messages, 
                    gemini_tools, 
                    generation_config
                )
            else:
                # Generate response without tools
                response = await self._generate_response(
                    gemini_messages, 
                    generation_config
                )
            
            return self._format_response(response)
            
        except Exception as e:
            raise Exception(f"Gemini API error: {str(e)}")
    
    def _convert_messages(self, messages: List[Dict[str, str]]) -> str:
        """Convert OpenAI-style messages to Gemini prompt format"""
        prompt_parts = []
        
        for message in messages:
            role = message.get("role", "user")
            content = message.get("content", "")
            
            if role == "system":
                prompt_parts.append(f"System: {content}")
            elif role == "user":
                prompt_parts.append(f"User: {content}")
            elif role == "assistant":
                prompt_parts.append(f"Assistant: {content}")
        
        return "\n\n".join(prompt_parts)
    
    def _convert_tools(self, tools: List[Dict]) -> List[Dict]:
        """Convert OpenAI-style tools to Gemini format"""
        gemini_tools = []
        
        for tool in tools:
            if tool.get("type") == "function":
                func_def = tool.get("function", {})
                gemini_tool = {
                    "name": func_def.get("name"),
                    "description": func_def.get("description"),
                    "parameters": func_def.get("parameters", {})
                }
                gemini_tools.append(gemini_tool)
        
        return gemini_tools
    
    async def _generate_response(self, prompt: str, config: Any) -> Any:
        """Generate response without tools"""
        loop = asyncio.get_event_loop()
        response = await loop.run_in_executor(
            None, 
            lambda: self.model.generate_content(prompt, generation_config=config)
        )
        return response
    
    async def _generate_with_tools(self, prompt: str, tools: List[Dict], config: Any) -> Any:
        """Generate response with tools (function calling)"""
        # For now, generate without tools and simulate tool calling
        # In a full implementation, you'd use Gemini's function calling capabilities
        loop = asyncio.get_event_loop()
        response = await loop.run_in_executor(
            None, 
            lambda: self.model.generate_content(prompt, generation_config=config)
        )
        return response
    
    def _format_response(self, response: Any) -> Dict[str, Any]:
        """Format Gemini response to OpenAI-style format"""
        try:
            content = response.text if hasattr(response, 'text') else str(response)
            
            return {
                "choices": [{
                    "message": {
                        "role": "assistant",
                        "content": content
                    },
                    "finish_reason": "stop"
                }],
                "usage": {
                    "prompt_tokens": 0,  # Gemini doesn't provide token counts
                    "completion_tokens": 0,
                    "total_tokens": 0
                }
            }
        except Exception as e:
            return {
                "choices": [{
                    "message": {
                        "role": "assistant", 
                        "content": f"Error processing response: {str(e)}"
                    },
                    "finish_reason": "error"
                }]
            }

# Global instance
gemini_client = GeminiClient()
