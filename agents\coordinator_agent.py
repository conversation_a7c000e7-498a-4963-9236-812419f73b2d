"""
Coordinator Agent - Central orchestrator for the multi-agent system
"""
import asyncio
from typing import Dict, List, Any, Optional, Tuple
import sys
import os
import json
from datetime import datetime

# Add the parent directory to the path to import modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from agents.base_agent import BaseAgent
from agents.healthcare_agent import HealthcareAgent
from agents.ai_research_agent import AIResearchAgent
from tools.memory_tools import SharedMemoryManager, store_shared_memory, retrieve_shared_memories
from tools.research_tools import summarize_research
from config import Config

class CoordinatorAgent(BaseAgent):
    """Coordinator Agent that orchestrates tasks between specialist agents"""
    
    def __init__(self):
        # Load system prompt
        prompt_path = os.path.join(os.path.dirname(os.path.dirname(__file__)), "prompts", "coordinator_prompt.md")
        with open(prompt_path, 'r', encoding='utf-8') as f:
            system_prompt = f.read()
        
        super().__init__(
            agent_id="coordinator_agent",
            name="Coordinator Agent",
            description="Central orchestrator for task distribution and result synthesis",
            system_prompt=system_prompt,
            tools=self._setup_coordinator_tools()
        )
        
        # Initialize specialist agents
        self.healthcare_agent = None
        self.ai_research_agent = None
        self.shared_memory = SharedMemoryManager()
        
        # Task management
        self.active_tasks = {}
        self.task_counter = 0
    
    async def initialize_agents(self):
        """Initialize specialist agents"""
        try:
            self.healthcare_agent = HealthcareAgent()
            self.ai_research_agent = AIResearchAgent()
            print("✅ Specialist agents initialized successfully")
        except Exception as e:
            print(f"❌ Error initializing specialist agents: {str(e)}")
            raise
    
    def _setup_coordinator_tools(self) -> List[Dict[str, Any]]:
        """Setup coordinator-specific tools"""
        coordinator_tools = [
            {
                "name": "delegate_to_healthcare",
                "description": "Delegate a task to the Healthcare Research Agent",
                "function": self._delegate_to_healthcare,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "task": {"type": "string", "description": "Task to delegate"},
                        "task_type": {"type": "string", "description": "Type of healthcare task"},
                        "context": {"type": "object", "description": "Additional context"}
                    },
                    "required": ["task"]
                }
            },
            {
                "name": "delegate_to_ai_research",
                "description": "Delegate a task to the AI Research Agent",
                "function": self._delegate_to_ai_research,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "task": {"type": "string", "description": "Task to delegate"},
                        "task_type": {"type": "string", "description": "Type of AI research task"},
                        "context": {"type": "object", "description": "Additional context"}
                    },
                    "required": ["task"]
                }
            },
            {
                "name": "coordinate_parallel_tasks",
                "description": "Execute multiple tasks in parallel across different agents",
                "function": self._coordinate_parallel_tasks,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "healthcare_task": {"type": "string", "description": "Task for healthcare agent"},
                        "ai_task": {"type": "string", "description": "Task for AI research agent"},
                        "synthesis_required": {"type": "boolean", "description": "Whether to synthesize results"}
                    },
                    "required": ["healthcare_task", "ai_task"]
                }
            },
            {
                "name": "synthesize_results",
                "description": "Synthesize results from multiple agents into a comprehensive report",
                "function": self._synthesize_results,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "results": {"type": "array", "description": "Results from different agents"},
                        "synthesis_type": {"type": "string", "description": "Type of synthesis required"},
                        "focus_areas": {"type": "array", "description": "Key focus areas for synthesis"}
                    },
                    "required": ["results"]
                }
            },
            {
                "name": "store_shared_insight",
                "description": "Store insights that can be shared across agents",
                "function": self._store_shared_insight,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "insight": {"type": "string", "description": "Insight to store"},
                        "source_agent": {"type": "string", "description": "Source agent"},
                        "target_agents": {"type": "array", "description": "Target agents for sharing"}
                    },
                    "required": ["insight", "source_agent"]
                }
            },
            {
                "name": "retrieve_shared_insights",
                "description": "Retrieve shared insights relevant to a query",
                "function": self._retrieve_shared_insights,
                "parameters": {
                    "type": "object",
                    "properties": {
                        "query": {"type": "string", "description": "Query for retrieving insights"},
                        "requesting_agent": {"type": "string", "description": "Agent requesting insights"}
                    },
                    "required": ["query", "requesting_agent"]
                }
            }
        ]
        return coordinator_tools
    
    async def _delegate_to_healthcare(self, task: str, task_type: str = None, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Delegate a task to the Healthcare Research Agent"""
        try:
            if not self.healthcare_agent:
                await self.initialize_agents()
            
            # Create task ID
            task_id = f"healthcare_{self.task_counter}"
            self.task_counter += 1
            
            # Store task in active tasks
            self.active_tasks[task_id] = {
                "agent": "healthcare",
                "task": task,
                "task_type": task_type,
                "context": context,
                "start_time": datetime.now().isoformat(),
                "status": "in_progress"
            }
            
            # Execute task
            if task_type and hasattr(self.healthcare_agent, 'specialized_task'):
                result = await self.healthcare_agent.specialized_task(task, task_type=task_type, **(context or {}))
            else:
                result = await self.healthcare_agent.process_message(task, context)
            
            # Update task status
            self.active_tasks[task_id]["status"] = "completed"
            self.active_tasks[task_id]["result"] = result
            self.active_tasks[task_id]["end_time"] = datetime.now().isoformat()
            
            # Store result in shared memory if significant
            if isinstance(result, dict) and len(str(result)) > 100:
                await self._store_shared_insight(
                    f"Healthcare research result: {str(result)[:500]}...",
                    "healthcare_agent",
                    ["ai_research_agent", "coordinator_agent"]
                )
            
            return {
                "task_id": task_id,
                "agent": "healthcare",
                "result": result,
                "status": "completed"
            }
            
        except Exception as e:
            return {"error": f"Healthcare delegation failed: {str(e)}", "task_id": task_id}
    
    async def _delegate_to_ai_research(self, task: str, task_type: str = None, context: Dict[str, Any] = None) -> Dict[str, Any]:
        """Delegate a task to the AI Research Agent"""
        try:
            if not self.ai_research_agent:
                await self.initialize_agents()
            
            # Create task ID
            task_id = f"ai_research_{self.task_counter}"
            self.task_counter += 1
            
            # Store task in active tasks
            self.active_tasks[task_id] = {
                "agent": "ai_research",
                "task": task,
                "task_type": task_type,
                "context": context,
                "start_time": datetime.now().isoformat(),
                "status": "in_progress"
            }
            
            # Execute task
            if task_type and hasattr(self.ai_research_agent, 'specialized_task'):
                result = await self.ai_research_agent.specialized_task(task, task_type=task_type, **(context or {}))
            else:
                result = await self.ai_research_agent.process_message(task, context)
            
            # Update task status
            self.active_tasks[task_id]["status"] = "completed"
            self.active_tasks[task_id]["result"] = result
            self.active_tasks[task_id]["end_time"] = datetime.now().isoformat()
            
            # Store result in shared memory if significant
            if isinstance(result, dict) and len(str(result)) > 100:
                await self._store_shared_insight(
                    f"AI research result: {str(result)[:500]}...",
                    "ai_research_agent",
                    ["healthcare_agent", "coordinator_agent"]
                )
            
            return {
                "task_id": task_id,
                "agent": "ai_research",
                "result": result,
                "status": "completed"
            }
            
        except Exception as e:
            return {"error": f"AI research delegation failed: {str(e)}", "task_id": task_id}
    
    async def _coordinate_parallel_tasks(
        self, 
        healthcare_task: str, 
        ai_task: str, 
        synthesis_required: bool = True
    ) -> Dict[str, Any]:
        """Execute multiple tasks in parallel across different agents"""
        try:
            # Create parallel tasks
            healthcare_coroutine = self._delegate_to_healthcare(healthcare_task)
            ai_research_coroutine = self._delegate_to_ai_research(ai_task)
            
            # Execute tasks in parallel
            healthcare_result, ai_research_result = await asyncio.gather(
                healthcare_coroutine,
                ai_research_coroutine,
                return_exceptions=True
            )
            
            # Handle exceptions
            if isinstance(healthcare_result, Exception):
                healthcare_result = {"error": str(healthcare_result)}
            if isinstance(ai_research_result, Exception):
                ai_research_result = {"error": str(ai_research_result)}
            
            results = {
                "healthcare_result": healthcare_result,
                "ai_research_result": ai_research_result,
                "execution_type": "parallel",
                "tasks": {
                    "healthcare_task": healthcare_task,
                    "ai_task": ai_task
                }
            }
            
            # Synthesize results if required
            if synthesis_required:
                synthesis = await self._synthesize_results([healthcare_result, ai_research_result])
                results["synthesis"] = synthesis
            
            return results
            
        except Exception as e:
            return {"error": f"Parallel coordination failed: {str(e)}"}
    
    async def _synthesize_results(
        self, 
        results: List[Dict[str, Any]], 
        synthesis_type: str = "comprehensive",
        focus_areas: List[str] = None
    ) -> Dict[str, Any]:
        """Synthesize results from multiple agents into a comprehensive report"""
        try:
            # Filter out error results
            valid_results = [r for r in results if not isinstance(r, dict) or "error" not in r]
            
            if not valid_results:
                return {"synthesis": "No valid results to synthesize", "status": "no_data"}
            
            # Extract key information from results
            synthesis_data = {
                "executive_summary": "",
                "key_findings": [],
                "cross_domain_insights": [],
                "recommendations": [],
                "methodology": [],
                "sources": []
            }
            
            # Process each result
            for i, result in enumerate(valid_results):
                agent_type = "unknown"
                if isinstance(result, dict):
                    if "healthcare" in str(result).lower():
                        agent_type = "healthcare"
                    elif "ai" in str(result).lower():
                        agent_type = "ai_research"
                
                # Extract findings
                if isinstance(result, dict):
                    if "result" in result:
                        synthesis_data["key_findings"].append({
                            "agent": agent_type,
                            "finding": str(result["result"])[:500] + "..." if len(str(result["result"])) > 500 else str(result["result"])
                        })
                    
                    # Extract sources if available
                    if "results" in result and isinstance(result["results"], list):
                        for source in result["results"][:3]:  # Top 3 sources
                            if isinstance(source, dict) and "url" in source:
                                synthesis_data["sources"].append(source["url"])
            
            # Generate cross-domain insights
            if len(valid_results) > 1:
                synthesis_data["cross_domain_insights"] = self._generate_cross_domain_insights(valid_results)
            
            # Generate executive summary
            synthesis_data["executive_summary"] = self._generate_executive_summary(synthesis_data)
            
            # Generate recommendations
            synthesis_data["recommendations"] = self._generate_synthesis_recommendations(synthesis_data)
            
            # Use research summarization tool
            summary = summarize_research([{"title": "Synthesis", "content": str(synthesis_data)}])
            
            return {
                "synthesis": synthesis_data,
                "summary": summary,
                "synthesis_type": synthesis_type,
                "focus_areas": focus_areas or [],
                "total_results_synthesized": len(valid_results),
                "status": "completed"
            }
            
        except Exception as e:
            return {"error": f"Result synthesis failed: {str(e)}"}
    
    async def _store_shared_insight(
        self, 
        insight: str, 
        source_agent: str, 
        target_agents: List[str] = None
    ) -> str:
        """Store insights that can be shared across agents"""
        try:
            memory_id = await store_shared_memory(
                insight,
                source_agent,
                target_agents,
                {"timestamp": datetime.now().isoformat(), "type": "cross_agent_insight"}
            )
            return memory_id
        except Exception as e:
            print(f"Error storing shared insight: {str(e)}")
            return ""
    
    async def _retrieve_shared_insights(self, query: str, requesting_agent: str) -> List[Dict[str, Any]]:
        """Retrieve shared insights relevant to a query"""
        try:
            insights = await retrieve_shared_memories(query, requesting_agent, limit=5)
            return insights
        except Exception as e:
            print(f"Error retrieving shared insights: {str(e)}")
            return []
    
    def _generate_cross_domain_insights(self, results: List[Dict[str, Any]]) -> List[str]:
        """Generate insights that span multiple domains"""
        insights = []
        
        # Look for common themes across results
        all_content = " ".join([str(result) for result in results])
        content_lower = all_content.lower()
        
        # Healthcare + AI intersections
        if any(term in content_lower for term in ['healthcare', 'medical']) and \
           any(term in content_lower for term in ['ai', 'artificial intelligence', 'machine learning']):
            insights.append("Identified potential AI applications in healthcare domain")
        
        # Research methodology intersections
        if any(term in content_lower for term in ['research', 'study', 'analysis']) and \
           len(results) > 1:
            insights.append("Multiple research perspectives provide comprehensive analysis")
        
        # Technology and health intersections
        if any(term in content_lower for term in ['technology', 'digital']) and \
           any(term in content_lower for term in ['health', 'patient', 'clinical']):
            insights.append("Technology integration opportunities in health sector identified")
        
        return insights
    
    def _generate_executive_summary(self, synthesis_data: Dict[str, Any]) -> str:
        """Generate an executive summary of the synthesis"""
        summary_parts = []
        
        if synthesis_data["key_findings"]:
            summary_parts.append(f"Analysis incorporated findings from {len(synthesis_data['key_findings'])} different research perspectives.")
        
        if synthesis_data["cross_domain_insights"]:
            summary_parts.append(f"Identified {len(synthesis_data['cross_domain_insights'])} cross-domain insights.")
        
        if synthesis_data["sources"]:
            summary_parts.append(f"Research based on {len(synthesis_data['sources'])} authoritative sources.")
        
        summary = " ".join(summary_parts) if summary_parts else "Comprehensive analysis completed across multiple domains."
        
        return summary
    
    def _generate_synthesis_recommendations(self, synthesis_data: Dict[str, Any]) -> List[str]:
        """Generate recommendations based on synthesized results"""
        recommendations = [
            "Consider interdisciplinary approaches for comprehensive understanding",
            "Validate findings through multiple authoritative sources",
            "Monitor developments across related domains for emerging opportunities"
        ]
        
        if synthesis_data["cross_domain_insights"]:
            recommendations.append("Explore identified cross-domain connections for innovation opportunities")
        
        return recommendations
    
    async def specialized_task(self, task: str, **kwargs) -> Dict[str, Any]:
        """Handle specialized coordination tasks"""
        task_type = kwargs.get('task_type', 'general_coordination')
        
        if task_type == 'parallel_research':
            healthcare_task = kwargs.get('healthcare_task', task)
            ai_task = kwargs.get('ai_task', task)
            return await self._coordinate_parallel_tasks(healthcare_task, ai_task)
        
        elif task_type == 'healthcare_delegation':
            return await self._delegate_to_healthcare(task, kwargs.get('healthcare_task_type'), kwargs.get('context'))
        
        elif task_type == 'ai_research_delegation':
            return await self._delegate_to_ai_research(task, kwargs.get('ai_task_type'), kwargs.get('context'))
        
        elif task_type == 'result_synthesis':
            results = kwargs.get('results', [])
            return await self._synthesize_results(results, kwargs.get('synthesis_type'), kwargs.get('focus_areas'))
        
        else:
            # Default coordination behavior
            response = await self.process_message(task, kwargs)
            return {"response": response, "task_type": "general_coordination"}
    
    def get_system_status(self) -> Dict[str, Any]:
        """Get comprehensive system status"""
        status = {
            "coordinator_status": self.get_status(),
            "specialist_agents": {
                "healthcare": self.healthcare_agent.get_status() if self.healthcare_agent else "Not initialized",
                "ai_research": self.ai_research_agent.get_status() if self.ai_research_agent else "Not initialized"
            },
            "active_tasks": len(self.active_tasks),
            "completed_tasks": len([t for t in self.active_tasks.values() if t.get("status") == "completed"]),
            "system_health": "Operational" if self.healthcare_agent and self.ai_research_agent else "Partial"
        }
        
        return status
