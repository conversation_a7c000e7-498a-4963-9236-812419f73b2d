# Multi-Agent System Setup Guide

## 🎯 Quick Setup (2 Minutes)

### Step 1: API Key Already Configured! ✅
The system is pre-configured with your Gemini API key - no setup needed!

### Step 2: Ready to Use
The environment is already configured and ready to run.

### Step 3: Install Dependencies
```bash
pip install -r requirements.txt
```

### Step 4: Run the System
```bash
python main.py
```

## 🚀 First Run

When you run the system for the first time:

1. **Interactive Mode**: Just run `python main.py`
2. **Direct Query**: Run `python main.py "Your question here"`
3. **Demo**: Run `python demo.py` to see system overview

## 🎮 Usage Examples

### Basic Queries
```bash
# Healthcare query
python main.py "What are the symptoms of diabetes?"

# AI research query  
python main.py "Explain transformer neural networks"

# Cross-domain query
python main.py "AI applications in medical diagnosis"
```

### Interactive Mode Commands
```
💬 Enter your query: What is machine learning?
💬 Enter your query: type:healthcare:Latest cancer treatments
💬 Enter your query: type:parallel:AI in healthcare
💬 Enter your query: status
💬 Enter your query: help
💬 Enter your query: quit
```

## 🔧 System Architecture

```
┌─────────────────────┐
│  Coordinator Agent  │ ← Routes tasks, synthesizes results
└─────────┬───────────┘
          │
    ┌─────┴─────┐
    │           │
┌───▼───┐   ┌───▼───┐
│Health │   │  AI   │ ← Specialist agents with domain expertise
│Agent  │   │Agent  │
└───────┘   └───────┘
    │           │
    └─────┬─────┘
          ▼
    ┌─────────┐
    │ Local   │ ← Local file-based memory
    │ Memory  │
    └─────────┘
```

## 🎯 Agent Capabilities

### Healthcare Research Agent
- ✅ Medical literature search
- ✅ Clinical evidence evaluation
- ✅ Health trend analysis
- ✅ Drug information assessment
- ✅ Regulatory compliance analysis

### AI Research Agent
- ✅ AI paper search and analysis
- ✅ Technology trend identification
- ✅ Model performance evaluation
- ✅ Ethics and bias analysis
- ✅ Application research

### Coordinator Agent
- ✅ Intelligent task routing
- ✅ Parallel task execution
- ✅ Result synthesis
- ✅ Cross-agent coordination
- ✅ Memory management

## 🔍 Query Routing

The system automatically routes queries:

| Query Type | Example | Routing |
|------------|---------|---------|
| Healthcare | "diabetes symptoms" | Healthcare Agent |
| AI/Tech | "machine learning" | AI Research Agent |
| Cross-domain | "AI in medicine" | Both Agents (Parallel) |
| General | "research methods" | Coordinator |

## 🛠️ Troubleshooting

### Common Issues

1. **Missing API Key**
   ```
   Error: GEMINI_API_KEY is required
   ```
   **Solution**: API key is pre-configured. Check your internet connection.

2. **Import Errors**
   ```
   ModuleNotFoundError: No module named 'agents'
   ```
   **Solution**: Run from project root directory

3. **Memory Errors**
   ```
   Error initializing memory
   ```
   **Solution**: Check memory path permissions and disk space

### Debug Mode
Add to `.env` file:
```env
DEBUG=true
```

## 📊 System Status

Check system health:
```bash
python main.py
# Then type: status
```

Output shows:
- Agent status (Ready/Active)
- Active tasks count
- Memory usage
- System health

## 🔄 Extending the System

### Add New Agent
1. Create new agent class in `agents/`
2. Inherit from `BaseAgent`
3. Add to coordinator initialization
4. Update query classification

### Add New Tools
1. Create tool functions in `tools/`
2. Register with appropriate agents
3. Update tool documentation

## 📈 Performance Tips

- Use parallel processing for independent tasks
- Cache frequently accessed information
- Monitor API usage and rate limits
- Optimize memory retrieval queries

## 🔒 Security Notes

- Store API keys in `.env` file (not in code)
- Use environment variables for sensitive data
- Local memory storage ensures privacy
- Monitor API usage and costs

## 📞 Getting Help

1. Check this setup guide
2. Review `README.md` for detailed documentation
3. Run `python demo.py` for system overview
4. Use `help` command in interactive mode

## 🎉 Success Indicators

You'll know the system is working when:
- ✅ No import errors on startup
- ✅ Agents initialize successfully
- ✅ Queries return relevant responses
- ✅ Memory stores and retrieves information
- ✅ Status command shows "Operational"

---

**Ready to start? Run `python main.py` and ask your first question!**
