# Mem0 Integration Guide

This guide explains how to connect your multi-agent system to your Mem0 server for enhanced memory storage and retrieval.

## 🔗 What's New

Your agents can now store and retrieve memories from your Mem0 server in addition to local storage. This provides:

- **Persistent Memory**: Memories stored in Mem0 survive system restarts
- **Advanced Search**: Better semantic search capabilities
- **MCP Integration**: Connect to your MCP link interface
- **Dual Storage**: Fallback to local storage if Mem0 is unavailable
- **Cross-Session Memory**: Memories persist across different sessions

## 🚀 Quick Setup

### 1. Configure Environment Variables

Add these to your `.env` file or set as environment variables:

```env
# Mem0 Integration Settings
MEM0_ENABLED=true
MEM0_SERVER_URL=http://localhost:3000
MEM0_MCP_ENDPOINT=http://localhost:8765/mcp/openmemory/sse/NEXT_PUBLIC_USER_ID
MEM0_USER_ID=agent_system_user
MEM0_TIMEOUT=30
MEM0_FALLBACK_LOCAL=true
```

### 2. Start Your Mem0 Server

Make sure your Mem0 server is running on `localhost:3000` (as shown in your screenshot).

### 3. Test the Connection

Run the test script to verify everything is working:

```bash
python mem0_test.py
```

## 📋 Configuration Options

| Variable | Default | Description |
|----------|---------|-------------|
| `MEM0_ENABLED` | `true` | Enable/disable Mem0 integration |
| `MEM0_SERVER_URL` | `http://localhost:3000` | Your Mem0 server URL |
| `MEM0_MCP_ENDPOINT` | `http://localhost:8765/mcp/openmemory/sse/NEXT_PUBLIC_USER_ID` | MCP endpoint from your interface |
| `MEM0_USER_ID` | `agent_system_user` | User ID for memory storage |
| `MEM0_TIMEOUT` | `30` | HTTP timeout in seconds |
| `MEM0_FALLBACK_LOCAL` | `true` | Use local storage if Mem0 fails |

## 🔧 How It Works

### Memory Storage Flow

1. **Agent processes a message** → Generates response
2. **Store interaction** → Saves to both local JSON and Mem0
3. **Success indicators** → ✅ for successful storage, ⚠️ for fallback

### Memory Retrieval Flow

1. **Agent needs context** → Searches for relevant memories
2. **Dual search** → Queries both local storage and Mem0
3. **Merge results** → Combines and deduplicates memories
4. **Relevance ranking** → Returns best matches

### Agent Integration

Each agent automatically uses the enhanced memory system:

```python
# Healthcare Agent storing a memory
healthcare_agent.process_message("What are diabetes treatments?")
# → Stores conversation in both local and Mem0

# AI Research Agent retrieving context
ai_agent.process_message("Explain transformers")
# → Retrieves relevant memories from both sources
```

## 🧪 Testing Your Setup

### Basic Connection Test

```bash
python mem0_test.py
```

This will:
- ✅ Test connection to Mem0 server
- 📝 Store and retrieve test memories
- 🤖 Test agent integration
- 📊 Show integration status

### Interactive Testing

The test script includes an interactive mode:

```
Commands:
  1. Test connection
  2. Test memory storage  
  3. Test agent integration
  4. Show status
  5. Store custom memory
  6. Search memories
  q. Quit
```

### Manual Testing

```python
from tools.memory_tools import EnhancedMemoryManager

# Create manager
manager = EnhancedMemoryManager("test_agent")

# Store memory
await manager.store_memory("Test content", {"category": "test"})

# Retrieve memories
memories = await manager.retrieve_memories("test", limit=5)
```

## 🔍 Monitoring Integration

### Success Indicators

When running your agents, look for these messages:

```
🔗 Agent healthcare_agent initialized with Mem0 integration
✅ Stored memory locally for agent healthcare_agent
✅ Stored memory in Mem0 for agent healthcare_agent
✅ Retrieved 3 memories from local storage
✅ Retrieved 2 memories from Mem0
```

### Warning Indicators

```
⚠️ Could not store in Mem0, using local storage: Connection failed
⚠️ Could not retrieve from Mem0: Timeout error
💾 Agent healthcare_agent initialized with local memory only
```

## 🛠️ Troubleshooting

### Connection Issues

**Problem**: `❌ Failed to connect to Mem0 server`

**Solutions**:
1. Check if Mem0 server is running on localhost:3000
2. Verify the server URL in your config
3. Check firewall settings
4. Try accessing http://localhost:3000 in your browser

### Memory Storage Issues

**Problem**: Memories not appearing in Mem0 interface

**Solutions**:
1. Check the user ID configuration
2. Verify API endpoints are correct
3. Check Mem0 server logs
4. Test with the interactive test script

### Performance Issues

**Problem**: Slow memory operations

**Solutions**:
1. Increase the timeout setting
2. Check network connectivity
3. Monitor Mem0 server performance
4. Consider disabling Mem0 temporarily

## 🔄 Fallback Behavior

If Mem0 is unavailable, the system automatically:

1. **Stores memories locally** in JSON files
2. **Retrieves from local storage** only
3. **Logs warnings** about Mem0 unavailability
4. **Continues normal operation** without interruption

## 📊 Memory Sources

Your agents now retrieve memories from multiple sources:

- **Local Storage**: Fast, always available, stored in `./memory_db/`
- **Mem0**: Advanced search, persistent, web interface
- **Combined Results**: Deduplicated and ranked by relevance

Each memory includes a `source` field indicating where it came from:
- `"source": "local"` - From local JSON files
- `"source": "mem0"` - From Mem0 server

## 🎯 Best Practices

1. **Keep Mem0 server running** for best experience
2. **Monitor connection status** in agent logs
3. **Use the test script** to verify setup
4. **Keep fallback enabled** for reliability
5. **Check memory interface** at localhost:3000

## 🔧 Advanced Configuration

### Custom MCP Endpoint

If your MCP endpoint is different, update the config:

```env
MEM0_MCP_ENDPOINT=http://your-server:port/mcp/path
```

### Multiple User IDs

For different agent groups:

```env
MEM0_USER_ID=healthcare_agents
# or
MEM0_USER_ID=research_agents
```

### Performance Tuning

```env
MEM0_TIMEOUT=60          # Longer timeout for slow networks
MEM0_FALLBACK_LOCAL=false # Disable fallback for Mem0-only mode
```

## 🎉 You're Ready!

Your multi-agent system is now integrated with Mem0! Your agents will automatically:

- 💾 Store conversations in both local and Mem0
- 🔍 Search across both memory sources
- 🔄 Fall back gracefully if Mem0 is unavailable
- 📊 Show you exactly what's happening in the logs

Run your agents normally with `python main.py` and watch the enhanced memory system in action!
