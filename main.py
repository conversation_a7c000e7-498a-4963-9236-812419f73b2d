"""
Main execution script for the Multi-Agent System
"""
import asyncio
import json
import sys
import os
from datetime import datetime
from typing import Dict, Any, Optional

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from agents.coordinator_agent import CoordinatorAgent
from config import Config

class MultiAgentSystem:
    """Main class for the multi-agent system"""
    
    def __init__(self):
        self.coordinator = None
        self.system_initialized = False
        self.session_id = f"session_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
    
    async def initialize(self):
        """Initialize the multi-agent system"""
        try:
            print("🚀 Initializing Multi-Agent System...")
            print(f"📋 Session ID: {self.session_id}")
            
            # Validate configuration
            Config.validate_config()
            print("✅ Configuration validated")
            
            # Initialize coordinator agent
            self.coordinator = CoordinatorAgent()
            print("✅ Coordinator agent created")
            
            # Initialize specialist agents through coordinator
            await self.coordinator.initialize_agents()
            print("✅ Specialist agents initialized")
            
            self.system_initialized = True
            print("🎉 Multi-Agent System initialized successfully!")
            
            # Display system status
            await self.display_system_status()
            
        except Exception as e:
            print(f"❌ Initialization failed: {str(e)}")
            raise
    
    async def display_system_status(self):
        """Display current system status"""
        if not self.coordinator:
            print("❌ System not initialized")
            return
        
        status = self.coordinator.get_system_status()
        print("\n📊 System Status:")
        print(f"   Coordinator: {status['coordinator_status']['name']} - {'Active' if status['coordinator_status']['is_active'] else 'Ready'}")
        print(f"   Healthcare Agent: {status['specialist_agents']['healthcare']['name'] if isinstance(status['specialist_agents']['healthcare'], dict) else 'Not Ready'}")
        print(f"   AI Research Agent: {status['specialist_agents']['ai_research']['name'] if isinstance(status['specialist_agents']['ai_research'], dict) else 'Not Ready'}")
        print(f"   Active Tasks: {status['active_tasks']}")
        print(f"   System Health: {status['system_health']}")
        print()
    
    async def process_query(self, query: str, query_type: str = "auto") -> Dict[str, Any]:
        """Process a user query through the multi-agent system"""
        if not self.system_initialized:
            raise RuntimeError("System not initialized. Call initialize() first.")
        
        print(f"🔍 Processing query: {query[:100]}{'...' if len(query) > 100 else ''}")
        print(f"📝 Query type: {query_type}")
        
        try:
            # Determine query type if auto
            if query_type == "auto":
                query_type = self._classify_query(query)
                print(f"🤖 Auto-classified as: {query_type}")
            
            # Route query based on type
            if query_type == "healthcare":
                result = await self.coordinator._delegate_to_healthcare(query)
            elif query_type == "ai_research":
                result = await self.coordinator._delegate_to_ai_research(query)
            elif query_type == "parallel":
                # Split query for parallel processing
                healthcare_task, ai_task = self._split_query_for_parallel(query)
                result = await self.coordinator._coordinate_parallel_tasks(healthcare_task, ai_task)
            else:
                # General coordination
                result = await self.coordinator.process_message(query)
            
            print("✅ Query processed successfully")
            return {
                "query": query,
                "query_type": query_type,
                "result": result,
                "session_id": self.session_id,
                "timestamp": datetime.now().isoformat()
            }
            
        except Exception as e:
            error_result = {
                "query": query,
                "query_type": query_type,
                "error": str(e),
                "session_id": self.session_id,
                "timestamp": datetime.now().isoformat()
            }
            print(f"❌ Query processing failed: {str(e)}")
            return error_result
    
    def _classify_query(self, query: str) -> str:
        """Classify query to determine appropriate routing"""
        query_lower = query.lower()
        
        # Healthcare keywords
        healthcare_keywords = [
            'health', 'medical', 'disease', 'treatment', 'clinical', 'patient',
            'drug', 'medication', 'therapy', 'diagnosis', 'symptom', 'healthcare',
            'hospital', 'doctor', 'nurse', 'pharmaceutical', 'epidemiology'
        ]
        
        # AI research keywords
        ai_keywords = [
            'ai', 'artificial intelligence', 'machine learning', 'deep learning',
            'neural network', 'algorithm', 'model', 'nlp', 'computer vision',
            'transformer', 'gpt', 'llm', 'automation', 'robotics', 'data science'
        ]
        
        # Check for healthcare keywords
        healthcare_score = sum(1 for keyword in healthcare_keywords if keyword in query_lower)
        
        # Check for AI keywords
        ai_score = sum(1 for keyword in ai_keywords if keyword in query_lower)
        
        # Check for both (parallel processing)
        if healthcare_score > 0 and ai_score > 0:
            return "parallel"
        elif healthcare_score > ai_score:
            return "healthcare"
        elif ai_score > healthcare_score:
            return "ai_research"
        else:
            return "general"
    
    def _split_query_for_parallel(self, query: str) -> tuple:
        """Split a query into healthcare and AI research components"""
        # Simple splitting strategy - can be enhanced
        healthcare_task = f"Analyze the healthcare aspects of: {query}"
        ai_task = f"Analyze the AI/technology aspects of: {query}"
        
        return healthcare_task, ai_task
    
    async def run_interactive_mode(self):
        """Run the system in interactive mode"""
        if not self.system_initialized:
            await self.initialize()
        
        print("\n🎯 Multi-Agent System - Interactive Mode")
        print("Type 'help' for commands, 'quit' to exit")
        print("-" * 50)
        
        while True:
            try:
                # Get user input
                user_input = input("\n💬 Enter your query: ").strip()
                
                if not user_input:
                    continue
                
                # Handle commands
                if user_input.lower() == 'quit':
                    print("👋 Goodbye!")
                    break
                elif user_input.lower() == 'help':
                    self._display_help()
                    continue
                elif user_input.lower() == 'status':
                    await self.display_system_status()
                    continue
                elif user_input.lower().startswith('type:'):
                    # Handle explicit type specification
                    parts = user_input.split(':', 1)
                    if len(parts) == 2:
                        query_type = parts[0].replace('type', '').strip()
                        query = parts[1].strip()
                        result = await self.process_query(query, query_type)
                    else:
                        print("❌ Invalid format. Use: type:healthcare:your query")
                        continue
                else:
                    # Process regular query
                    result = await self.process_query(user_input)
                
                # Display result
                self._display_result(result)
                
            except KeyboardInterrupt:
                print("\n👋 Goodbye!")
                break
            except Exception as e:
                print(f"❌ Error: {str(e)}")
    
    def _display_help(self):
        """Display help information"""
        help_text = """
🆘 Multi-Agent System Help

Commands:
  help                    - Show this help message
  status                  - Display system status
  quit                    - Exit the system

Query Types:
  [query]                 - Auto-classify and process query
  type:healthcare:[query] - Route to healthcare agent
  type:ai_research:[query]- Route to AI research agent
  type:parallel:[query]   - Process with both agents in parallel
  type:general:[query]    - Process with coordinator

Examples:
  What are the latest treatments for diabetes?
  type:ai_research:Latest developments in transformer models
  type:parallel:AI applications in medical diagnosis
        """
        print(help_text)
    
    def _display_result(self, result: Dict[str, Any]):
        """Display query result in a formatted way"""
        print("\n📋 Result:")
        print("-" * 30)
        
        if "error" in result:
            print(f"❌ Error: {result['error']}")
            return
        
        # Display basic info
        print(f"Query Type: {result.get('query_type', 'Unknown')}")
        print(f"Timestamp: {result.get('timestamp', 'Unknown')}")
        
        # Display result content
        result_content = result.get('result', {})
        if isinstance(result_content, dict):
            if 'response' in result_content:
                print(f"\n📝 Response:\n{result_content['response']}")
            elif 'synthesis' in result_content:
                print(f"\n🔬 Synthesis:\n{result_content['synthesis']}")
            else:
                print(f"\n📊 Result:\n{json.dumps(result_content, indent=2)[:500]}...")
        else:
            print(f"\n📝 Response:\n{str(result_content)}")
        
        print("-" * 30)

async def main():
    """Main function"""
    print("🤖 Multi-Agent System with OpenAI SDK, Google Gemini, and Mem0")
    print("=" * 60)
    
    # Create and initialize system
    system = MultiAgentSystem()
    
    try:
        # Initialize the system
        await system.initialize()
        
        # Check if command line arguments are provided
        if len(sys.argv) > 1:
            # Process command line query
            query = " ".join(sys.argv[1:])
            result = await system.process_query(query)
            system._display_result(result)
        else:
            # Run in interactive mode
            await system.run_interactive_mode()
    
    except KeyboardInterrupt:
        print("\n👋 System interrupted by user")
    except Exception as e:
        print(f"❌ System error: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # Run the main function
    asyncio.run(main())
