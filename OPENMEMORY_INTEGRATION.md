# OpenMemory MCP Integration

This document explains how the OpenMemory MCP (Model Context Protocol) integration works with your multi-agent system.

## 🔗 How It Works

The integration automatically stores agent conversation memories in your OpenMemory server using the MCP protocol. Every time an agent processes a message, the conversation is stored both locally (for reliability) and in OpenMemory (for centralized access).

### Integration Points

1. **Agent Initialization**: Each agent automatically connects to OpenMemory when created
2. **Memory Storage**: Every conversation is stored in both local storage and OpenMemory
3. **Memory Retrieval**: Agents can access memories from both sources for context
4. **Fallback**: If OpenMemory is unavailable, local storage ensures continuity

## 📋 Configuration

The integration is configured in `config.py`:

```python
MEM0_CONFIG = {
    "enabled": True,  # Enable/disable OpenMemory integration
    "server_url": "http://localhost:3000",  # OpenMemory web interface
    "mcp_endpoint": "http://localhost:8765/mcp/openmemory/sse/NEXT_PUBLIC_USER_ID",  # MCP endpoint
    "user_id": "agent_system_user",  # User ID for memory storage
    "timeout": 30,  # Connection timeout
    "fallback_to_local": True  # Use local storage if OpenMemory fails
}
```

## 🚀 Usage

### Automatic Memory Storage

When you have conversations with your agents, memories are automatically stored:

```python
# Start your agents normally
python main.py

# Have a conversation
> What are the latest treatments for diabetes?

# Memory is automatically stored in:
# 1. Local storage (./memory_db/)
# 2. OpenMemory server (visible at http://localhost:3000)
```

### Viewing Stored Memories

1. **OpenMemory Web Interface**: Visit `http://localhost:3000` to see all stored memories
2. **Local Storage**: Check `./memory_db/` directory for JSON files

## 🔧 Technical Details

### Memory Storage Flow

1. Agent processes user message
2. Agent generates response
3. Conversation is stored locally (immediate, reliable)
4. Conversation is sent to OpenMemory via MCP (centralized, accessible)
5. If OpenMemory fails, local storage ensures no data loss

### MCP Protocol

The integration attempts to use the MCP endpoint with different approaches:
- Direct POST to MCP endpoint
- Alternative endpoint variations
- Graceful fallback to logging

### File Structure

```
tools/
├── mem0_adapter.py          # OpenMemory MCP integration
├── memory_tools.py          # Enhanced memory manager
└── ...

config.py                    # Configuration including MCP settings
```

## 🎯 Benefits

✅ **Automatic**: No manual intervention required  
✅ **Reliable**: Local fallback ensures no data loss  
✅ **Centralized**: All memories accessible via OpenMemory web interface  
✅ **Seamless**: Works with existing agent conversation flow  
✅ **Configurable**: Easy to enable/disable or modify settings  

## 🔍 Monitoring

Look for these log messages to confirm the integration is working:

```
🔗 Agent healthcare_agent initialized with OpenMemory integration
✅ Stored memory locally for agent healthcare_agent
✅ Stored memory in OpenMemory for agent healthcare_agent
```

## 🛠️ Troubleshooting

### OpenMemory Not Connecting

1. Ensure OpenMemory server is running on `localhost:3000`
2. Check MCP endpoint is accessible on `localhost:8765`
3. Verify configuration in `config.py`

### Memories Not Appearing in Web Interface

1. Check OpenMemory web interface at `http://localhost:3000`
2. Look for log messages indicating successful storage
3. Verify MCP endpoint configuration

### Disabling Integration

Set `MEM0_CONFIG["enabled"] = False` in `config.py` to disable OpenMemory integration and use only local storage.

## 🎉 Ready to Use!

The integration is now configured and ready. Simply start your agents and have conversations - memories will be automatically stored in both local storage and OpenMemory for the best of both worlds!
