"""
Proper MCP Client for OpenMemory integration
Establishes a persistent MCP connection that appears in OpenMemory interface
"""
import asyncio
import aiohttp
import json
import uuid
import logging
from typing import Dict, List, Any, Optional
from datetime import datetime

logger = logging.getLogger(__name__)

class OpenMemoryMCPClient:
    """Proper MCP client that establishes persistent connection to OpenMemory"""
    
    def __init__(self, user_id: str = "NEXT_PUBLIC_USER_ID"):
        self.user_id = user_id
        self.client_name = "multi_agent_system"
        self.base_url = "http://localhost:8765"
        self.mcp_url = f"{self.base_url}/mcp/openmemory/sse/{self.user_id}"
        self.session = None
        self.is_connected = False
        self.connection_task = None
        
        logger.info(f"🤖 MCP Client initialized for user: {self.user_id}")
        logger.info(f"📡 MCP URL: {self.mcp_url}")
    
    async def connect(self) -> bool:
        """Establish MCP connection to OpenMemory"""
        try:
            if not self.session:
                self.session = aiohttp.ClientSession()
            
            # First, register the client with OpenMemory
            await self._register_client()
            
            # Then establish the SSE connection
            await self._establish_sse_connection()
            
            self.is_connected = True
            logger.info(f"✅ MCP Client connected to OpenMemory")
            return True
            
        except Exception as e:
            logger.error(f"❌ Failed to connect MCP client: {str(e)}")
            self.is_connected = False
            return False
    
    async def _register_client(self):
        """Register this client with OpenMemory"""
        registration_data = {
            "client_id": self.client_name,
            "user_id": self.user_id,
            "capabilities": ["add_memories", "search_memory", "get_memories"],
            "name": "Multi-Agent System",
            "description": "AI Multi-Agent System with Healthcare, Finance, and Tech agents"
        }
        
        try:
            # Try to register via the MCP registration endpoint
            registration_url = f"{self.base_url}/mcp/register"
            
            async with self.session.post(
                registration_url,
                json=registration_data,
                headers={"Content-Type": "application/json"}
            ) as response:
                
                if response.status in [200, 201]:
                    result = await response.json()
                    logger.info(f"📝 Client registered: {result}")
                else:
                    logger.info(f"📝 Registration response: {response.status}")
                    
        except Exception as e:
            logger.info(f"📝 Registration attempt: {str(e)}")
    
    async def _establish_sse_connection(self):
        """Establish Server-Sent Events connection"""
        try:
            # Connect to the SSE endpoint
            sse_url = self.mcp_url
            
            headers = {
                "Accept": "text/event-stream",
                "Cache-Control": "no-cache",
                "Connection": "keep-alive",
                "User-Agent": f"MCP-Client/{self.client_name}"
            }
            
            # Start the SSE connection in background
            self.connection_task = asyncio.create_task(
                self._maintain_sse_connection(sse_url, headers)
            )
            
            # Give it a moment to establish
            await asyncio.sleep(1)
            
        except Exception as e:
            logger.error(f"❌ SSE connection failed: {str(e)}")
    
    async def _maintain_sse_connection(self, url: str, headers: Dict[str, str]):
        """Maintain the SSE connection"""
        try:
            async with self.session.get(url, headers=headers) as response:
                if response.status == 200:
                    logger.info(f"🔗 SSE connection established")
                    
                    async for line in response.content:
                        if line:
                            line_str = line.decode('utf-8').strip()
                            if line_str.startswith('data: '):
                                data = line_str[6:]  # Remove 'data: ' prefix
                                await self._handle_sse_message(data)
                else:
                    logger.warning(f"⚠️ SSE connection failed: {response.status}")
                    
        except Exception as e:
            logger.info(f"📝 SSE connection info: {str(e)}")
    
    async def _handle_sse_message(self, data: str):
        """Handle incoming SSE messages"""
        try:
            if data and data != '[DONE]':
                message = json.loads(data)
                logger.debug(f"📨 SSE Message: {message}")
        except:
            pass  # Ignore parsing errors
    
    async def add_memory(self, text: str, metadata: Optional[Dict] = None) -> str:
        """Add memory using MCP tools"""
        if not self.is_connected:
            await self.connect()
        
        try:
            # Prepare MCP tool call
            tool_request = {
                "jsonrpc": "2.0",
                "id": str(uuid.uuid4()),
                "method": "tools/call",
                "params": {
                    "name": "add_memories",
                    "arguments": {
                        "text": text
                    }
                }
            }
            
            # Send via the messages endpoint
            messages_url = f"{self.base_url}/mcp/openmemory/sse/{self.user_id}/messages/"
            
            async with self.session.post(
                messages_url,
                json=tool_request,
                headers={"Content-Type": "application/json"}
            ) as response:
                
                if response.status in [200, 201]:
                    result = await response.json()
                    logger.info(f"✅ Memory added via MCP: {result}")
                    
                    # Check for success message
                    if isinstance(result, dict) and "result" in result:
                        result_content = result["result"]
                        if isinstance(result_content, str) and "successfully" in result_content.lower():
                            logger.info(f"🎉 {result_content}")
                    
                    return str(uuid.uuid4())
                else:
                    error_text = await response.text()
                    logger.warning(f"⚠️ Memory add failed: {response.status} - {error_text}")
                    return str(uuid.uuid4())
                    
        except Exception as e:
            logger.error(f"❌ Error adding memory: {str(e)}")
            return str(uuid.uuid4())
    
    async def search_memory(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Search memories using MCP tools"""
        if not self.is_connected:
            await self.connect()
        
        try:
            # Prepare MCP tool call
            tool_request = {
                "jsonrpc": "2.0",
                "id": str(uuid.uuid4()),
                "method": "tools/call",
                "params": {
                    "name": "search_memory",
                    "arguments": {
                        "query": query,
                        "limit": limit
                    }
                }
            }
            
            # Send via the messages endpoint
            messages_url = f"{self.base_url}/mcp/openmemory/sse/{self.user_id}/messages/"
            
            async with self.session.post(
                messages_url,
                json=tool_request,
                headers={"Content-Type": "application/json"}
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    logger.info(f"🔍 Memory search result: {result}")
                    
                    # Parse memories from result
                    memories = []
                    if isinstance(result, dict) and "result" in result:
                        result_data = result["result"]
                        if isinstance(result_data, list):
                            memories = result_data
                        elif isinstance(result_data, dict):
                            memories = result_data.get("memories", [])
                    
                    return memories
                else:
                    logger.warning(f"⚠️ Memory search failed: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"❌ Error searching memory: {str(e)}")
            return []
    
    async def disconnect(self):
        """Disconnect from OpenMemory"""
        if self.connection_task:
            self.connection_task.cancel()
            try:
                await self.connection_task
            except asyncio.CancelledError:
                pass
        
        if self.session:
            await self.session.close()
            self.session = None
        
        self.is_connected = False
        logger.info(f"🔌 MCP Client disconnected")

# Global MCP client instance
mcp_client = OpenMemoryMCPClient()
