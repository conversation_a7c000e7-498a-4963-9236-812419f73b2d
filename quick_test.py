"""
Quick test to verify the system configuration
"""
import sys
import os

# Add current directory to path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_configuration():
    """Test system configuration"""
    print("🧪 Testing Multi-Agent System Configuration")
    print("=" * 50)
    
    try:
        # Test config import
        from config import Config
        print("✅ Config module imported successfully")
        
        # Test API key
        api_key = Config.GEMINI_API_KEY
        if api_key and api_key.startswith("AIza"):
            print("✅ Gemini API key configured")
            print(f"   Key: {api_key[:10]}...{api_key[-5:]}")
        else:
            print("❌ Gemini API key not found")
            return False
        
        # Test other config values
        print(f"✅ Model: {Config.GEMINI_MODEL}")
        print(f"✅ Temperature: {Config.GEMINI_TEMPERATURE}")
        print(f"✅ Max Tokens: {Config.GEMINI_MAX_TOKENS}")
        print(f"✅ Memory Path: {Config.MEMORY_CONFIG['storage_path']}")
        
        # Test agent configs
        for agent_type in ['healthcare', 'ai_research', 'coordinator']:
            config = Config.get_agent_config(agent_type)
            if config:
                print(f"✅ {agent_type.title()} agent config loaded")
            else:
                print(f"⚠️  {agent_type.title()} agent config empty")
        
        print("\n🎉 Configuration test passed!")
        print("System is ready to use!")
        return True
        
    except Exception as e:
        print(f"❌ Configuration test failed: {str(e)}")
        return False

def test_imports():
    """Test module imports"""
    print("\n📦 Testing Module Imports")
    print("-" * 30)
    
    modules_to_test = [
        ('config', 'Config'),
        ('utils.gemini_client', 'gemini_client'),
        ('tools.memory_tools', 'AgentMemoryManager'),
        ('tools.research_tools', 'web_search'),
        ('agents.base_agent', 'BaseAgent'),
        ('agents.healthcare_agent', 'HealthcareAgent'),
        ('agents.ai_research_agent', 'AIResearchAgent'),
        ('agents.coordinator_agent', 'CoordinatorAgent')
    ]
    
    success_count = 0
    for module_name, class_name in modules_to_test:
        try:
            module = __import__(module_name, fromlist=[class_name])
            getattr(module, class_name)
            print(f"✅ {module_name}.{class_name}")
            success_count += 1
        except Exception as e:
            print(f"❌ {module_name}.{class_name}: {str(e)}")
    
    print(f"\n📊 Import Results: {success_count}/{len(modules_to_test)} successful")
    return success_count == len(modules_to_test)

def main():
    """Main test function"""
    print("🤖 Multi-Agent System Quick Test")
    print("=" * 50)
    
    # Test configuration
    config_ok = test_configuration()
    
    # Test imports
    imports_ok = test_imports()
    
    # Final result
    print("\n" + "=" * 50)
    if config_ok and imports_ok:
        print("🎉 ALL TESTS PASSED!")
        print("✅ System is ready to use")
        print("\nNext steps:")
        print("1. Run: python main.py")
        print("2. Or try: python demo.py")
    else:
        print("❌ Some tests failed")
        print("Please check the error messages above")

if __name__ == "__main__":
    main()
