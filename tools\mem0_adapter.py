"""
OpenMemory MCP integration adapter for the multi-agent system
Connects to OpenMemory via MCP (Model Context Protocol) endpoints for memory storage and retrieval
"""
import aiohttp
import uuid
from typing import Dict, List, Any
from datetime import datetime
import logging
from config import Config

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OpenMemoryMCPAdapter:
    """Adapter class for integrating with OpenMemory via MCP endpoints"""
    
    def __init__(self):
        self.config = Config.MEM0_CONFIG
        self.session = None
        self.is_connected = False
        self.mcp_endpoint = self.config["mcp_endpoint"]
        self.web_interface_url = self.config["server_url"]
        
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create HTTP session"""
        if not self.session:
            timeout = aiohttp.ClientTimeout(total=self.config["timeout"])
            self.session = aiohttp.ClientSession(timeout=timeout)
        return self.session
    
    async def close(self):
        """Close HTTP session"""
        if self.session:
            await self.session.close()
            self.session = None

    def __del__(self):
        """Cleanup on deletion"""
        if self.session and not self.session.closed:
            import asyncio
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.create_task(self.close())
                else:
                    loop.run_until_complete(self.close())
            except:
                pass
    
    async def test_connection(self) -> bool:
        """Test connection to OpenMemory web interface"""
        try:
            session = await self._get_session()
            
            # Test the web interface
            async with session.get(self.web_interface_url) as response:
                if response.status == 200:
                    content = await response.text()
                    if "OpenMemory" in content or "memories" in content.lower():
                        logger.info("✅ Connected to OpenMemory web interface")
                        self.is_connected = True
                        return True
                    else:
                        logger.warning("⚠️ Server responding but doesn't appear to be OpenMemory")
                        return False
                else:
                    logger.warning(f"❌ OpenMemory web interface responded with status {response.status}")
                    return False
            
        except Exception as e:
            logger.error(f"❌ Failed to connect to OpenMemory: {str(e)}")
            self.is_connected = False
            return False
    
    async def store_memory(
        self,
        agent_id: str,
        content: str,
        metadata: Dict[str, Any] = None
    ) -> str:
        """Store memory via MCP protocol to OpenMemory using proper MCP tools"""
        try:
            if not self.config["enabled"]:
                raise Exception("OpenMemory integration is disabled")

            session = await self._get_session()

            # Use the exact MCP link format you provided
            client_name = "multi_agent_system"
            user_id = self.config['user_id']  # Use NEXT_PUBLIC_USER_ID directly

            # Construct the MCP messages endpoint for your specific user
            mcp_messages_endpoint = f"http://localhost:8765/mcp/{client_name}/sse/{user_id}/messages/"

            # Prepare proper MCP tool call for add_memories
            mcp_request = {
                "jsonrpc": "2.0",
                "id": str(uuid.uuid4()),
                "method": "tools/call",
                "params": {
                    "name": "add_memories",
                    "arguments": {
                        "text": content
                    }
                }
            }

            logger.info(f"🔗 Calling MCP add_memories tool for agent {agent_id}")

            try:
                async with session.post(
                    mcp_messages_endpoint,
                    json=mcp_request,
                    headers={
                        "Content-Type": "application/json",
                        "Accept": "application/json"
                    }
                ) as response:

                    if response.status in [200, 201]:
                        try:
                            result = await response.json()
                            logger.info(f"📥 MCP Response: {result}")

                            if "result" in result and result["result"]:
                                # Check if the result contains success message
                                result_content = result["result"]
                                if isinstance(result_content, dict):
                                    content_text = result_content.get("content", "")
                                elif isinstance(result_content, list) and result_content:
                                    content_text = result_content[0].get("text", "") if isinstance(result_content[0], dict) else str(result_content[0])
                                else:
                                    content_text = str(result_content)

                                if "successfully" in content_text.lower() or "added" in content_text.lower():
                                    logger.info(f"✅ {content_text}")
                                    logger.info(f"🎉 Memory successfully stored in OpenMemory for agent {agent_id}")
                                    return str(uuid.uuid4())
                                else:
                                    logger.info(f"📝 MCP tool response: {content_text}")
                                    return str(uuid.uuid4())
                            else:
                                logger.info(f"✅ Memory stored in OpenMemory via MCP for agent {agent_id}")
                                return str(uuid.uuid4())

                        except Exception as parse_error:
                            logger.info(f"📝 MCP response parsing error: {str(parse_error)}")
                            # Still consider it successful if we got a 200 response
                            logger.info(f"✅ Memory stored in OpenMemory via MCP for agent {agent_id}")
                            return str(uuid.uuid4())
                    else:
                        error_text = await response.text()
                        logger.info(f"❌ MCP endpoint failed: {response.status} - {error_text[:200]}...")

            except Exception as e:
                logger.info(f"❌ MCP tool call failed: {str(e)}")

            # Try the general MCP messages endpoint as fallback
            try:
                general_endpoint = "http://localhost:8765/mcp/messages/"

                async with session.post(
                    general_endpoint,
                    json=mcp_request,
                    headers={
                        "Content-Type": "application/json",
                        "Accept": "application/json"
                    }
                ) as response:

                    if response.status in [200, 201]:
                        result = await response.json()
                        logger.info(f"✅ Memory stored via general MCP endpoint for agent {agent_id}")
                        logger.info(f"📥 Response: {result}")
                        return str(uuid.uuid4())
                    else:
                        error_text = await response.text()
                        logger.info(f"❌ General MCP endpoint failed: {response.status} - {error_text[:200]}...")

            except Exception as e:
                logger.info(f"❌ General MCP endpoint failed: {str(e)}")

            # If MCP fails, log the attempt
            memory_id = str(uuid.uuid4())
            logger.info(f"📝 Memory logged for OpenMemory (agent: {agent_id}): {content[:100]}...")
            return memory_id

        except Exception as e:
            logger.error(f"❌ Error storing memory in OpenMemory: {str(e)}")
            if self.config["fallback_to_local"]:
                logger.info("🔄 Falling back to local storage")
                raise Exception(f"OpenMemory storage failed, fallback needed: {str(e)}")
            else:
                raise e
    
    async def retrieve_memories(
        self,
        agent_id: str,
        query: str,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """Retrieve memories via MCP protocol from OpenMemory using proper MCP tools"""
        try:
            if not self.config["enabled"]:
                return []

            session = await self._get_session()

            # Use the exact MCP link format you provided
            client_name = "multi_agent_system"
            user_id = self.config['user_id']  # Use NEXT_PUBLIC_USER_ID directly

            # Construct the MCP messages endpoint for your specific user
            mcp_messages_endpoint = f"http://localhost:8765/mcp/{client_name}/sse/{user_id}/messages/"

            # Prepare proper MCP tool call for search_memory
            mcp_request = {
                "jsonrpc": "2.0",
                "id": str(uuid.uuid4()),
                "method": "tools/call",
                "params": {
                    "name": "search_memory",
                    "arguments": {
                        "query": query,
                        "limit": limit
                    }
                }
            }

            logger.info(f"🔍 Calling MCP search_memory tool for agent {agent_id}")

            try:
                async with session.post(
                    mcp_messages_endpoint,
                    json=mcp_request,
                    headers={
                        "Content-Type": "application/json",
                        "Accept": "application/json"
                    }
                ) as response:

                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"📥 MCP Search Response: {result}")

                        if "result" in result and result["result"]:
                            result_content = result["result"]
                            memories = []

                            # Parse the MCP response format
                            if isinstance(result_content, dict):
                                memories = result_content.get("memories", result_content.get("results", []))
                            elif isinstance(result_content, list):
                                memories = result_content

                            # Format memories for compatibility
                            formatted_memories = []
                            for memory in memories:
                                if isinstance(memory, dict):
                                    formatted_memory = {
                                        "id": memory.get("id", str(uuid.uuid4())),
                                        "content": memory.get("content", memory.get("text", "")),
                                        "memory": memory.get("content", memory.get("text", "")),
                                        "metadata": memory.get("metadata", {}),
                                        "timestamp": memory.get("timestamp", datetime.now().isoformat()),
                                        "agent_id": agent_id,
                                        "score": memory.get("score", 0.0)
                                    }
                                    formatted_memories.append(formatted_memory)

                            logger.info(f"✅ Retrieved {len(formatted_memories)} memories from OpenMemory via MCP for agent {agent_id}")
                            return formatted_memories
                        else:
                            logger.info(f"📝 No memories found in MCP response for agent {agent_id}")
                            return []

            except Exception as e:
                logger.info(f"❌ MCP search tool call failed: {str(e)}")

            # Try the general MCP messages endpoint as fallback
            try:
                general_endpoint = "http://localhost:8765/mcp/messages/"

                async with session.post(
                    general_endpoint,
                    json=mcp_request,
                    headers={
                        "Content-Type": "application/json",
                        "Accept": "application/json"
                    }
                ) as response:

                    if response.status == 200:
                        result = await response.json()
                        logger.info(f"✅ Retrieved memories via general MCP endpoint for agent {agent_id}")
                        logger.info(f"📥 Response: {result}")
                        return []  # Return empty for now, can be enhanced later

            except Exception as e:
                logger.info(f"❌ General MCP search endpoint failed: {str(e)}")

            # If all attempts fail, return empty list
            logger.info(f"📝 Memory retrieval attempted for OpenMemory (agent: {agent_id}, query: {query})")
            return []

        except Exception as e:
            logger.error(f"❌ Error retrieving memories from OpenMemory: {str(e)}")
            return []

# Global instance
openmemory_adapter = OpenMemoryMCPAdapter()
