# Multi-Agent System Environment Configuration
# The Gemini API key is already configured in the system

# Google Gemini API Configuration (Optional - override if needed)
# GEMINI_API_KEY=your_gemini_api_key_here

# Gemini Model Configuration
GEMINI_MODEL=gemini-1.5-flash
GEMINI_TEMPERATURE=0.7
GEMINI_MAX_TOKENS=8192

# System Configuration
MAX_TURNS=20
TIMEOUT_SECONDS=300
DEBUG=false

# Local Memory Configuration
MEMORY_PATH=./memory_db
MAX_MEMORIES_PER_AGENT=1000
MEMORY_RETENTION_DAYS=30
