# Multi-Agent System with OpenAI SDK and Google Gemini

A sophisticated multi-agent research system that combines the OpenAI Agents SDK architecture with Google Gemini as the LLM backend and local memory management. The system features three specialized agents working together to provide comprehensive research and analysis.

## 🏗️ Architecture Overview

### Agent Hierarchy
- **Coordinator Agent**: Central orchestrator that manages task distribution and result synthesis
- **Healthcare Research Agent**: Specializes in medical research, health trends, and clinical analysis
- **AI Research Agent**: Focuses on AI/ML research, technology trends, and technical innovation

### Key Features
- **Multi-Agent Coordination**: Agents work independently or collaboratively based on task requirements
- **Google Gemini Integration**: Uses Gemini as the underlying LLM instead of OpenAI models
- **Local Memory Management**: Simple file-based memory system for persistent context across agents
- **Parallel Processing**: Execute multiple tasks simultaneously across different agents
- **Intelligent Task Routing**: Automatic classification and routing of queries to appropriate agents
- **Result Synthesis**: Comprehensive synthesis of results from multiple agents

## 🚀 Quick Start

### Prerequisites
- Python 3.8 or higher
- Google Gemini API key (already configured)

### Installation

1. **Clone or download the project**
   ```bash
   cd "Documents\OPENAI SDK"
   ```

2. **Install dependencies**
   ```bash
   pip install -r requirements.txt
   ```

3. **Environment already configured**
   The system is pre-configured with the Gemini API key and ready to use!

4. **Run the system**
   ```bash
   python main.py
   ```

## 🎯 Usage Examples

### Interactive Mode
```bash
python main.py
```

### Command Line Query
```bash
python main.py "What are the latest AI applications in healthcare?"
```

### Specific Agent Routing
In interactive mode, you can route queries to specific agents:
```
type:healthcare:Latest treatments for diabetes
type:ai_research:Transformer model developments
type:parallel:AI applications in medical diagnosis
```

## 🔧 Configuration

### Environment Variables
- `GEMINI_API_KEY`: Pre-configured with your API key
- `GEMINI_MODEL`: Gemini model to use (default: gemini-2.0-flash-exp)
- `GEMINI_TEMPERATURE`: Temperature for text generation (default: 0.7)
- `GEMINI_MAX_TOKENS`: Maximum tokens per response (default: 8192)
- `MEMORY_PATH`: Path for local memory storage (default: ./memory_db)

### Agent Configuration
Each agent can be configured in `config.py`:
- Expertise areas
- Tool configurations
- Memory settings
- Model parameters

## 🛠️ System Components

### Core Modules

#### `agents/base_agent.py`
- Base class for all agents
- Gemini integration
- Memory management
- Tool execution framework

#### `agents/coordinator_agent.py`
- Central orchestrator
- Task distribution logic
- Result synthesis
- Cross-agent communication

#### `agents/healthcare_agent.py`
- Medical research specialization
- Clinical evidence evaluation
- Health trend analysis
- Drug information assessment

#### `agents/ai_research_agent.py`
- AI/ML research focus
- Technology trend analysis
- Model evaluation
- Ethics analysis

#### `utils/gemini_client.py`
- Google Gemini API wrapper
- OpenAI SDK compatibility layer
- Response formatting

#### `tools/memory_tools.py`
- Local file-based memory system
- Agent memory management
- Shared memory coordination

#### `tools/research_tools.py`
- Web search capabilities
- Content analysis
- Sentiment analysis
- Research summarization

## 🎨 Features in Detail

### Multi-Agent Coordination
- **Agent-as-Tool Pattern**: Coordinator uses specialist agents as tools
- **Parallel Execution**: Multiple agents work simultaneously
- **Result Synthesis**: Intelligent combination of multi-agent results
- **Shared Memory**: Cross-agent knowledge sharing

### Healthcare Research Capabilities
- Medical literature search
- Clinical evidence evaluation
- Health trend analysis
- Drug information assessment
- Regulatory compliance analysis

### AI Research Capabilities
- AI paper search and analysis
- Technology trend identification
- Model performance evaluation
- Ethics and bias analysis
- Application research

### Memory Management
- **Agent Memory**: Individual agent memory for context retention using local files
- **Shared Memory**: Cross-agent knowledge sharing through local storage
- **Persistent Storage**: Long-term memory across sessions stored locally
- **Context Retrieval**: Simple text-based memory retrieval with relevance scoring

## 🔍 Query Classification

The system automatically classifies queries and routes them appropriately:

- **Healthcare Queries**: Medical, health, clinical, pharmaceutical topics
- **AI Research Queries**: AI, ML, technology, algorithm topics
- **Parallel Queries**: Topics requiring both healthcare and AI expertise
- **General Queries**: Handled by the coordinator for synthesis

## 📊 System Status and Monitoring

### Status Commands
- `status`: Display current system status
- `help`: Show available commands
- Agent activity monitoring
- Task completion tracking

### Performance Metrics
- Task completion rates
- Agent utilization
- Memory usage
- Response times

## 🔒 Security and Privacy

### API Key Management
- Environment variable storage
- No hardcoded credentials
- Secure API communication

### Memory Privacy
- Agent-specific memory isolation
- Controlled cross-agent sharing
- Local file-based storage for privacy

## 🚨 Troubleshooting

### Common Issues

1. **Gemini API Key Error**
   ```
   Error: GEMINI_API_KEY is required
   ```
   Solution: The API key is pre-configured. If you see this error, check your internet connection.

2. **Module Import Errors**
   ```
   ModuleNotFoundError: No module named 'agents'
   ```
   Solution: Ensure you're running from the project root directory

3. **Memory Initialization Errors**
   ```
   Error initializing memory
   ```
   Solution: Check memory path permissions and disk space

### Debug Mode
Enable debug logging by setting:
```env
DEBUG=true
```

## 🔄 Extending the System

### Adding New Agents
1. Create a new agent class inheriting from `BaseAgent`
2. Define specialized tools and capabilities
3. Add agent to coordinator initialization
4. Update query classification logic

### Adding New Tools
1. Create tool functions in `tools/` directory
2. Register tools with appropriate agents
3. Update tool documentation

### Custom Memory Providers
1. Extend LocalMemoryStore class
2. Update memory configuration in config.py
3. Test with existing agents

## 📈 Performance Optimization

### Best Practices
- Use parallel processing for independent tasks
- Implement result caching for repeated queries
- Optimize memory retrieval queries
- Monitor API usage and rate limits

### Scaling Considerations
- Agent pool management
- Load balancing across agents
- Memory storage optimization
- API quota management

## 🤝 Contributing

### Development Setup
1. Fork the repository
2. Create a feature branch
3. Implement changes with tests
4. Submit a pull request

### Code Standards
- Follow Python PEP 8
- Add type hints
- Include docstrings
- Write unit tests

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🙏 Acknowledgments

- OpenAI for the Agents SDK architecture inspiration
- Google for the Gemini API
- The open-source community for various tools and libraries

## 📞 Support

For issues and questions:
1. Check the troubleshooting section
2. Review the configuration guide
3. Create an issue with detailed information
4. Include system status and error logs

---

**Note**: This system is designed for research and educational purposes. Always verify information from authoritative sources, especially for healthcare-related queries.
