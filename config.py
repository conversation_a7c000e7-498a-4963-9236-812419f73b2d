"""
Configuration file for the Multi-Agent System
"""
import os
from dotenv import load_dotenv
from typing import Dict, Any

# Load environment variables
load_dotenv()

class Config:
    """Configuration class for the multi-agent system"""

    # API Keys
    GEMINI_API_KEY = os.getenv("GEMINI_API_KEY", "AIzaSyBZWuoZTnFC5oNjbEV9BaBnodtEt3C7_Ns")

    # Gemini Model Configuration
    GEMINI_MODEL = os.getenv("GEMINI_MODEL", "gemini-2.0-flash-exp")
    GEMINI_TEMPERATURE = float(os.getenv("GEMINI_TEMPERATURE", "0.7"))
    GEMINI_MAX_TOKENS = int(os.getenv("GEMINI_MAX_TOKENS", "8192"))

    # Agent Configuration
    MAX_TURNS = int(os.getenv("MAX_TURNS", "20"))
    TIMEOUT_SECONDS = int(os.getenv("TIMEOUT_SECONDS", "300"))

    # Local Memory Configuration
    MEMORY_CONFIG = {
        "storage_path": os.getenv("MEMORY_PATH", "./memory_db"),
        "max_memories_per_agent": int(os.getenv("MAX_MEMORIES_PER_AGENT", "1000")),
        "memory_retention_days": int(os.getenv("MEMORY_RETENTION_DAYS", "30"))
    }

    # OpenMemory MCP Integration Configuration
    MEM0_CONFIG = {
        "enabled": os.getenv("MEM0_ENABLED", "true").lower() == "true",
        "server_url": os.getenv("MEM0_SERVER_URL", "http://localhost:3000"),
        "mcp_endpoint": os.getenv("MEM0_MCP_ENDPOINT", "http://localhost:8765/mcp/openmemory/sse/NEXT_PUBLIC_USER_ID"),
        "user_id": os.getenv("MEM0_USER_ID", "agent_system_user"),
        "timeout": int(os.getenv("MEM0_TIMEOUT", "30")),
        "fallback_to_local": os.getenv("MEM0_FALLBACK_TO_LOCAL", "true").lower() == "true"
    }

    # Agent Roles and Descriptions
    AGENT_CONFIGS = {
        "healthcare": {
            "name": "Healthcare Research Agent",
            "description": "Specializes in medical research, health trends, clinical studies, and healthcare analysis",
            "expertise": ["medical research", "clinical trials", "health policy", "epidemiology", "pharmaceutical research"]
        },
        "ai_research": {
            "name": "AI Research Agent", 
            "description": "Focuses on AI/ML research, technology trends, algorithm analysis, and technical innovation",
            "expertise": ["machine learning", "deep learning", "AI ethics", "computer vision", "natural language processing"]
        },
        "coordinator": {
            "name": "Coordinator Agent",
            "description": "Orchestrates tasks between specialist agents and synthesizes comprehensive results",
            "expertise": ["task coordination", "result synthesis", "project management", "strategic planning"]
        }
    }
    
    @classmethod
    def validate_config(cls) -> bool:
        """Validate that required configuration is present"""
        if not cls.GEMINI_API_KEY:
            raise ValueError("GEMINI_API_KEY is required. Please set it in your environment variables.")
        return True
    
    @classmethod
    def get_agent_config(cls, agent_type: str) -> Dict[str, Any]:
        """Get configuration for a specific agent type"""
        return cls.AGENT_CONFIGS.get(agent_type, {})

# Validate configuration on import
Config.validate_config()
