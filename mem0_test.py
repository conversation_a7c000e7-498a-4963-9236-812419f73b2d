"""
Test script for Mem0 integration with the multi-agent system
"""
import asyncio
import sys
import os

# Add the current directory to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from config import Config
from tools.mem0_adapter import mem0_adapter
from tools.memory_tools import EnhancedMemoryManager
from agents.healthcare_agent import HealthcareAgent
from agents.ai_research_agent import AIResearchAgent
from agents.coordinator_agent import CoordinatorAgent

async def test_mem0_connection():
    """Test connection to Mem0 server"""
    print("🔍 Testing Mem0 connection...")
    print(f"Server URL: {Config.MEM0_CONFIG['server_url']}")
    print(f"MCP Endpoint: {Config.MEM0_CONFIG['mcp_endpoint']}")
    print(f"Enabled: {Config.MEM0_CONFIG['enabled']}")
    
    if not Config.MEM0_CONFIG["enabled"]:
        print("❌ Mem0 integration is disabled in config")
        return False
    
    # Test connection
    connected = await mem0_adapter.test_connection()
    if connected:
        print("✅ Successfully connected to Mem0 server!")
        return True
    else:
        print("❌ Failed to connect to Mem0 server")
        print("Make sure your Mem0 server is running on localhost:3000")
        return False

async def test_memory_storage():
    """Test storing and retrieving memories"""
    print("\n🧪 Testing memory storage and retrieval...")
    
    # Create a test agent
    test_agent_id = "test_agent"
    memory_manager = EnhancedMemoryManager(test_agent_id)
    
    # Test storing a memory
    test_content = "This is a test memory for the multi-agent system integration with Mem0"
    test_metadata = {
        "test": True,
        "category": "integration_test",
        "importance": "high"
    }
    
    print(f"📝 Storing test memory: {test_content[:50]}...")
    memory_id = await memory_manager.store_memory(test_content, test_metadata)
    
    if memory_id:
        print(f"✅ Memory stored with ID: {memory_id}")
    else:
        print("❌ Failed to store memory")
        return False
    
    # Test retrieving memories
    print("🔍 Retrieving memories with query 'test memory'...")
    memories = await memory_manager.retrieve_memories("test memory", limit=3)
    
    if memories:
        print(f"✅ Retrieved {len(memories)} memories:")
        for i, memory in enumerate(memories, 1):
            content = memory.get("content", memory.get("memory", ""))
            source = memory.get("source", "unknown")
            print(f"  {i}. [{source}] {content[:100]}...")
    else:
        print("❌ No memories retrieved")
        return False
    
    return True

async def test_agent_integration():
    """Test agent integration with Mem0"""
    print("\n🤖 Testing agent integration with Mem0...")
    
    # Create test agents
    healthcare_agent = HealthcareAgent()
    ai_research_agent = AIResearchAgent()
    
    # Test healthcare agent
    print("🏥 Testing Healthcare Agent...")
    healthcare_response = await healthcare_agent.process_message(
        "What are the latest treatments for diabetes?",
        {"test_mode": True}
    )
    print(f"Healthcare Agent Response: {healthcare_response[:100]}...")
    
    # Test AI research agent
    print("🔬 Testing AI Research Agent...")
    ai_response = await ai_research_agent.process_message(
        "Explain transformer neural networks",
        {"test_mode": True}
    )
    print(f"AI Research Agent Response: {ai_response[:100]}...")
    
    # Test memory retrieval from previous conversations
    print("🧠 Testing memory retrieval from agent conversations...")
    healthcare_memories = await healthcare_agent._retrieve_memories_wrapper("diabetes", limit=2)
    ai_memories = await ai_research_agent._retrieve_memories_wrapper("transformer", limit=2)
    
    print(f"Healthcare memories found: {len(healthcare_memories)}")
    print(f"AI Research memories found: {len(ai_memories)}")
    
    return True

async def show_mem0_status():
    """Show current Mem0 integration status"""
    print("\n📊 Mem0 Integration Status")
    print("=" * 50)
    print(f"Enabled: {Config.MEM0_CONFIG['enabled']}")
    print(f"Server URL: {Config.MEM0_CONFIG['server_url']}")
    print(f"MCP Endpoint: {Config.MEM0_CONFIG['mcp_endpoint']}")
    print(f"User ID: {Config.MEM0_CONFIG['user_id']}")
    print(f"Timeout: {Config.MEM0_CONFIG['timeout']}s")
    print(f"Fallback to Local: {Config.MEM0_CONFIG['fallback_to_local']}")
    
    # Test connection status
    if Config.MEM0_CONFIG["enabled"]:
        connected = await mem0_adapter.test_connection()
        print(f"Connection Status: {'✅ Connected' if connected else '❌ Disconnected'}")
    else:
        print("Connection Status: ⚠️ Disabled")

async def interactive_mem0_test():
    """Interactive test interface"""
    print("\n🎮 Interactive Mem0 Test Interface")
    print("Commands:")
    print("  1. Test connection")
    print("  2. Test memory storage")
    print("  3. Test agent integration")
    print("  4. Show status")
    print("  5. Store custom memory")
    print("  6. Search memories")
    print("  q. Quit")
    
    while True:
        try:
            choice = input("\n💬 Enter command (1-6 or q): ").strip().lower()
            
            if choice == 'q':
                break
            elif choice == '1':
                await test_mem0_connection()
            elif choice == '2':
                await test_memory_storage()
            elif choice == '3':
                await test_agent_integration()
            elif choice == '4':
                await show_mem0_status()
            elif choice == '5':
                content = input("Enter memory content: ").strip()
                if content:
                    manager = EnhancedMemoryManager("interactive_test")
                    memory_id = await manager.store_memory(content, {"source": "interactive"})
                    print(f"✅ Stored memory with ID: {memory_id}")
            elif choice == '6':
                query = input("Enter search query: ").strip()
                if query:
                    manager = EnhancedMemoryManager("interactive_test")
                    memories = await manager.retrieve_memories(query, limit=5)
                    print(f"Found {len(memories)} memories:")
                    for i, memory in enumerate(memories, 1):
                        content = memory.get("content", memory.get("memory", ""))
                        source = memory.get("source", "unknown")
                        print(f"  {i}. [{source}] {content}")
            else:
                print("❌ Invalid command")
                
        except KeyboardInterrupt:
            break
        except Exception as e:
            print(f"❌ Error: {str(e)}")

async def main():
    """Main test function"""
    print("🚀 Mem0 Integration Test Suite")
    print("=" * 50)
    
    try:
        # Show current status
        await show_mem0_status()
        
        # Test connection
        connected = await test_mem0_connection()
        
        if connected:
            # Run memory tests
            await test_memory_storage()
            
            # Test agent integration
            await test_agent_integration()
            
            print("\n✅ All tests completed successfully!")
            print("\n🎮 Starting interactive mode...")
            await interactive_mem0_test()
        else:
            print("\n❌ Connection failed. Please check your Mem0 server.")
            print("Make sure it's running on localhost:3000")
            
    except Exception as e:
        print(f"❌ Test suite error: {str(e)}")
    finally:
        # Clean up
        await mem0_adapter.close()
        print("\n👋 Test suite finished")

if __name__ == "__main__":
    asyncio.run(main())
