"""
Mem0 integration adapter for the multi-agent system
Connects to mem0 server and MCP endpoints for memory storage and retrieval
"""
import asyncio
import aiohttp
import json
import uuid
from typing import Dict, List, Any, Optional
from datetime import datetime
import logging
from config import Config

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class Mem0Adapter:
    """Adapter class for integrating with Mem0 server and MCP endpoints"""
    
    def __init__(self):
        self.config = Config.MEM0_CONFIG
        self.session = None
        self.is_connected = False
        
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create HTTP session"""
        if not self.session:
            timeout = aiohttp.ClientTimeout(total=self.config["timeout"])
            self.session = aiohttp.ClientSession(timeout=timeout)
        return self.session
    
    async def close(self):
        """Close HTTP session"""
        if self.session:
            await self.session.close()
            self.session = None
    
    async def test_connection(self) -> bool:
        """Test connection to mem0 server"""
        try:
            session = await self._get_session()

            # Try multiple endpoints to find the right one
            test_endpoints = [
                "/",
                "/health",
                "/api/health",
                "/v1/health",
                "/status"
            ]

            for endpoint in test_endpoints:
                try:
                    async with session.get(f"{self.config['server_url']}{endpoint}") as response:
                        if response.status in [200, 404]:  # 404 might mean server is running but endpoint doesn't exist
                            if response.status == 200:
                                logger.info(f"✅ Connected to Mem0 server via {endpoint}")
                            else:
                                logger.info(f"✅ Mem0 server is running (found via {endpoint})")
                            self.is_connected = True
                            return True
                except Exception:
                    continue

            logger.warning("❌ Could not find a working endpoint on Mem0 server")
            return False

        except Exception as e:
            logger.error(f"❌ Failed to connect to Mem0 server: {str(e)}")
            self.is_connected = False
            return False
    
    async def store_memory(
        self, 
        agent_id: str, 
        content: str, 
        metadata: Dict[str, Any] = None
    ) -> str:
        """Store memory in Mem0 server"""
        try:
            if not self.config["enabled"]:
                raise Exception("Mem0 integration is disabled")
            
            session = await self._get_session()
            
            # Prepare memory data for Mem0
            memory_data = {
                "messages": [
                    {
                        "role": "user",
                        "content": content
                    }
                ],
                "user_id": f"{self.config['user_id']}_{agent_id}",
                "agent_id": agent_id,
                "metadata": {
                    "timestamp": datetime.now().isoformat(),
                    "source": "multi_agent_system",
                    **(metadata or {})
                }
            }
            
            # Try different API endpoints for storing memories
            api_endpoints = [
                "/v1/memories",
                "/api/v1/memories",
                "/memories",
                "/api/memories"
            ]

            response = None
            for endpoint in api_endpoints:
                try:
                    async with session.post(
                        f"{self.config['server_url']}{endpoint}",
                        json=memory_data,
                        headers={"Content-Type": "application/json"}
                    ) as resp:
                        response = resp
                        if resp.status in [200, 201]:
                            break
                except Exception:
                    continue

            if response:
                
                if response.status in [200, 201]:
                    result = await response.json()
                    memory_id = result.get("id", str(uuid.uuid4()))
                    logger.info(f"✅ Stored memory in Mem0 for agent {agent_id}")
                    return memory_id
                else:
                    error_text = await response.text()
                    logger.error(f"❌ Failed to store memory in Mem0: {response.status} - {error_text}")
                    raise Exception(f"Mem0 API error: {response.status}")
                    
        except Exception as e:
            logger.error(f"❌ Error storing memory in Mem0: {str(e)}")
            if self.config["fallback_to_local"]:
                logger.info("🔄 Falling back to local storage")
                raise Exception(f"Mem0 storage failed, fallback needed: {str(e)}")
            else:
                raise e
    
    async def retrieve_memories(
        self, 
        agent_id: str, 
        query: str, 
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """Retrieve memories from Mem0 server"""
        try:
            if not self.config["enabled"]:
                return []
            
            session = await self._get_session()
            
            # Search memories in Mem0 - try different endpoints
            search_params = {
                "query": query,
                "user_id": f"{self.config['user_id']}_{agent_id}",
                "limit": limit
            }

            search_endpoints = [
                "/v1/memories/search",
                "/api/v1/memories/search",
                "/memories/search",
                "/api/memories/search",
                "/v1/memories",
                "/memories"
            ]

            response = None
            for endpoint in search_endpoints:
                try:
                    async with session.get(
                        f"{self.config['server_url']}{endpoint}",
                        params=search_params
                    ) as resp:
                        response = resp
                        if resp.status == 200:
                            break
                except Exception:
                    continue

            if response:
                
                if response.status == 200:
                    result = await response.json()
                    memories = result.get("memories", [])
                    
                    # Format memories for compatibility with existing system
                    formatted_memories = []
                    for memory in memories:
                        formatted_memory = {
                            "id": memory.get("id", str(uuid.uuid4())),
                            "content": memory.get("memory", memory.get("text", "")),
                            "memory": memory.get("memory", memory.get("text", "")),
                            "metadata": memory.get("metadata", {}),
                            "timestamp": memory.get("created_at", datetime.now().isoformat()),
                            "agent_id": agent_id,
                            "score": memory.get("score", 0.0)
                        }
                        formatted_memories.append(formatted_memory)
                    
                    logger.info(f"✅ Retrieved {len(formatted_memories)} memories from Mem0 for agent {agent_id}")
                    return formatted_memories
                else:
                    error_text = await response.text()
                    logger.error(f"❌ Failed to retrieve memories from Mem0: {response.status} - {error_text}")
                    return []
                    
        except Exception as e:
            logger.error(f"❌ Error retrieving memories from Mem0: {str(e)}")
            return []
    
    async def get_all_memories(self, agent_id: str) -> List[Dict[str, Any]]:
        """Get all memories for an agent from Mem0"""
        try:
            if not self.config["enabled"]:
                return []
            
            session = await self._get_session()
            
            params = {
                "user_id": f"{self.config['user_id']}_{agent_id}",
                "limit": 1000  # Get a large number of memories
            }
            
            async with session.get(
                f"{self.config['server_url']}/v1/memories",
                params=params
            ) as response:
                
                if response.status == 200:
                    result = await response.json()
                    memories = result.get("memories", [])
                    
                    # Format memories
                    formatted_memories = []
                    for memory in memories:
                        formatted_memory = {
                            "id": memory.get("id", str(uuid.uuid4())),
                            "content": memory.get("memory", memory.get("text", "")),
                            "memory": memory.get("memory", memory.get("text", "")),
                            "metadata": memory.get("metadata", {}),
                            "timestamp": memory.get("created_at", datetime.now().isoformat()),
                            "agent_id": agent_id
                        }
                        formatted_memories.append(formatted_memory)
                    
                    logger.info(f"✅ Retrieved {len(formatted_memories)} total memories from Mem0 for agent {agent_id}")
                    return formatted_memories
                else:
                    logger.error(f"❌ Failed to get all memories from Mem0: {response.status}")
                    return []
                    
        except Exception as e:
            logger.error(f"❌ Error getting all memories from Mem0: {str(e)}")
            return []
    
    async def delete_memory(self, memory_id: str) -> bool:
        """Delete a memory from Mem0"""
        try:
            if not self.config["enabled"]:
                return False
            
            session = await self._get_session()
            
            async with session.delete(
                f"{self.config['server_url']}/v1/memories/{memory_id}"
            ) as response:
                
                if response.status in [200, 204]:
                    logger.info(f"✅ Deleted memory {memory_id} from Mem0")
                    return True
                else:
                    logger.error(f"❌ Failed to delete memory from Mem0: {response.status}")
                    return False
                    
        except Exception as e:
            logger.error(f"❌ Error deleting memory from Mem0: {str(e)}")
            return False

# Global instance
mem0_adapter = Mem0Adapter()
