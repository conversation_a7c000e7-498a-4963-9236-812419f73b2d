"""
OpenMemory MCP integration adapter for the multi-agent system
Connects to OpenMemory via MCP (Model Context Protocol) endpoints for memory storage and retrieval
"""
import aiohttp
import uuid
from typing import Dict, List, Any
from datetime import datetime
import logging
from config import Config

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OpenMemoryMCPAdapter:
    """Adapter class for integrating with OpenMemory via MCP endpoints"""
    
    def __init__(self):
        self.config = Config.MEM0_CONFIG
        self.session = None
        self.is_connected = False
        self.mcp_endpoint = self.config["mcp_endpoint"]
        self.web_interface_url = self.config["server_url"]
        
    async def _get_session(self) -> aiohttp.ClientSession:
        """Get or create HTTP session"""
        if not self.session:
            timeout = aiohttp.ClientTimeout(total=self.config["timeout"])
            self.session = aiohttp.ClientSession(timeout=timeout)
        return self.session
    
    async def close(self):
        """Close HTTP session"""
        if self.session:
            await self.session.close()
            self.session = None

    def __del__(self):
        """Cleanup on deletion"""
        if self.session and not self.session.closed:
            import asyncio
            try:
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    loop.create_task(self.close())
                else:
                    loop.run_until_complete(self.close())
            except:
                pass
    
    async def test_connection(self) -> bool:
        """Test connection to OpenMemory web interface"""
        try:
            session = await self._get_session()
            
            # Test the web interface
            async with session.get(self.web_interface_url) as response:
                if response.status == 200:
                    content = await response.text()
                    if "OpenMemory" in content or "memories" in content.lower():
                        logger.info("✅ Connected to OpenMemory web interface")
                        self.is_connected = True
                        return True
                    else:
                        logger.warning("⚠️ Server responding but doesn't appear to be OpenMemory")
                        return False
                else:
                    logger.warning(f"❌ OpenMemory web interface responded with status {response.status}")
                    return False
            
        except Exception as e:
            logger.error(f"❌ Failed to connect to OpenMemory: {str(e)}")
            self.is_connected = False
            return False
    
    async def store_memory(
        self, 
        agent_id: str, 
        content: str, 
        metadata: Dict[str, Any] = None
    ) -> str:
        """Store memory via MCP protocol to OpenMemory"""
        try:
            if not self.config["enabled"]:
                raise Exception("OpenMemory integration is disabled")
            
            session = await self._get_session()
            
            # Use the correct MCP protocol format
            # Based on OpenMemory docs: the endpoint should be /mcp/{client_name}/sse/{user_id}
            client_name = "multi_agent_system"
            user_id = f"{self.config['user_id']}_{agent_id}"

            # Construct the correct MCP endpoint based on OpenAPI docs
            mcp_base = self.mcp_endpoint.replace("/sse/NEXT_PUBLIC_USER_ID", "")
            # Try both the user-specific and general messages endpoints
            user_specific_endpoint = f"{mcp_base}/sse/{user_id}/messages/"
            general_messages_endpoint = f"{mcp_base.replace('/openmemory', '')}/messages/"

            # Prepare MCP tool call for add_memories
            mcp_request = {
                "jsonrpc": "2.0",
                "id": str(uuid.uuid4()),
                "method": "tools/call",
                "params": {
                    "name": "add_memories",
                    "arguments": {
                        "text": content,
                        "metadata": {
                            "agent_id": agent_id,
                            "timestamp": datetime.now().isoformat(),
                            "source": "multi_agent_system",
                            **(metadata or {})
                        }
                    }
                }
            }

            # Try the MCP messages endpoints
            mcp_endpoints = [user_specific_endpoint, general_messages_endpoint]

            for endpoint in mcp_endpoints:
                try:
                    async with session.post(
                        endpoint,
                        json=mcp_request,
                        headers={
                            "Content-Type": "application/json",
                            "Accept": "application/json",
                            "X-User-ID": user_id,
                            "X-Client-Name": client_name
                        }
                    ) as response:

                        if response.status in [200, 201]:
                            try:
                                result = await response.json()
                                if "result" in result:
                                    memory_id = result["result"].get("id", str(uuid.uuid4()))
                                    logger.info(f"✅ Stored memory in OpenMemory via MCP for agent {agent_id}")
                                    return memory_id
                                elif "error" not in result:
                                    memory_id = str(uuid.uuid4())
                                    logger.info(f"✅ Stored memory in OpenMemory via MCP for agent {agent_id}")
                                    return memory_id
                            except:
                                # If no JSON response, still consider it successful
                                memory_id = str(uuid.uuid4())
                                logger.info(f"✅ Stored memory in OpenMemory via MCP for agent {agent_id}")
                                return memory_id
                        else:
                            error_text = await response.text()
                            logger.info(f"MCP endpoint {endpoint} failed: {response.status} - {error_text[:200]}...")

                except Exception as e:
                    logger.info(f"MCP endpoint {endpoint} failed: {str(e)}")

            # Try alternative REST API approach
            try:
                api_endpoint = f"{mcp_base.replace('/mcp/openmemory', '')}/api/v1/memories/"
                # Use the correct format based on OpenAPI schema
                memory_data = {
                    "user_id": user_id,
                    "text": content,
                    "metadata": {
                        "agent_id": agent_id,
                        "timestamp": datetime.now().isoformat(),
                        "source": "multi_agent_system",
                        **(metadata or {})
                    },
                    "infer": True,
                    "app": "multi_agent_system"
                }

                async with session.post(
                    api_endpoint,
                    json=memory_data,
                    headers={
                        "Content-Type": "application/json",
                        "Accept": "application/json"
                    }
                ) as response:

                    if response.status in [200, 201]:
                        try:
                            result = await response.json()
                            memory_id = result.get("id", str(uuid.uuid4()))
                            logger.info(f"✅ Stored memory in OpenMemory via REST API for agent {agent_id}")
                            return memory_id
                        except:
                            memory_id = str(uuid.uuid4())
                            logger.info(f"✅ Stored memory in OpenMemory via REST API for agent {agent_id}")
                            return memory_id

            except Exception as e:
                logger.info(f"REST API endpoint failed: {str(e)}")
            
            # If all MCP attempts fail, log the memory for debugging
            memory_id = str(uuid.uuid4())
            logger.info(f"📝 Memory logged for OpenMemory (agent: {agent_id}): {content[:100]}...")
            return memory_id
                    
        except Exception as e:
            logger.error(f"❌ Error storing memory in OpenMemory: {str(e)}")
            if self.config["fallback_to_local"]:
                logger.info("🔄 Falling back to local storage")
                raise Exception(f"OpenMemory storage failed, fallback needed: {str(e)}")
            else:
                raise e
    
    async def retrieve_memories(
        self,
        agent_id: str,
        query: str,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """Retrieve memories via MCP protocol from OpenMemory"""
        try:
            if not self.config["enabled"]:
                return []

            session = await self._get_session()

            # Use the correct MCP protocol format
            client_name = "multi_agent_system"
            user_id = f"{self.config['user_id']}_{agent_id}"

            # Construct the correct MCP endpoint
            mcp_base = self.mcp_endpoint.replace("/sse/NEXT_PUBLIC_USER_ID", "")
            mcp_messages_endpoint = f"{mcp_base}/messages"

            # Prepare MCP tool call for search_memory
            mcp_request = {
                "jsonrpc": "2.0",
                "id": str(uuid.uuid4()),
                "method": "tools/call",
                "params": {
                    "name": "search_memory",
                    "arguments": {
                        "query": query,
                        "limit": limit
                    }
                }
            }

            # Try the MCP messages endpoint
            try:
                async with session.post(
                    mcp_messages_endpoint,
                    json=mcp_request,
                    headers={
                        "Content-Type": "application/json",
                        "Accept": "application/json",
                        "X-User-ID": user_id,
                        "X-Client-Name": client_name
                    }
                ) as response:

                    if response.status == 200:
                        result = await response.json()
                        if "result" in result:
                            memories = result["result"].get("memories", result["result"].get("results", []))

                            # Format memories for compatibility
                            formatted_memories = []
                            for memory in memories:
                                formatted_memory = {
                                    "id": memory.get("id", str(uuid.uuid4())),
                                    "content": memory.get("content", memory.get("text", "")),
                                    "memory": memory.get("content", memory.get("text", "")),
                                    "metadata": memory.get("metadata", {}),
                                    "timestamp": memory.get("timestamp", datetime.now().isoformat()),
                                    "agent_id": agent_id,
                                    "score": memory.get("score", 0.0)
                                }
                                formatted_memories.append(formatted_memory)

                            logger.info(f"✅ Retrieved {len(formatted_memories)} memories from OpenMemory via MCP for agent {agent_id}")
                            return formatted_memories

            except Exception as e:
                logger.debug(f"MCP search endpoint failed: {str(e)}")

            # Try alternative REST API approach
            try:
                api_endpoint = f"{mcp_base.replace('/mcp/openmemory', '')}/api/v1/memories/search"
                search_data = {
                    "query": query,
                    "user_id": user_id,
                    "limit": limit
                }

                async with session.post(
                    api_endpoint,
                    json=search_data,
                    headers={
                        "Content-Type": "application/json",
                        "Accept": "application/json"
                    }
                ) as response:

                    if response.status == 200:
                        result = await response.json()
                        memories = result.get("memories", result.get("results", []))

                        # Format memories for compatibility
                        formatted_memories = []
                        for memory in memories:
                            formatted_memory = {
                                "id": memory.get("id", str(uuid.uuid4())),
                                "content": memory.get("content", memory.get("text", "")),
                                "memory": memory.get("content", memory.get("text", "")),
                                "metadata": memory.get("metadata", {}),
                                "timestamp": memory.get("timestamp", datetime.now().isoformat()),
                                "agent_id": agent_id,
                                "score": memory.get("score", 0.0)
                            }
                            formatted_memories.append(formatted_memory)

                        logger.info(f"✅ Retrieved {len(formatted_memories)} memories from OpenMemory via REST API for agent {agent_id}")
                        return formatted_memories

            except Exception as e:
                logger.debug(f"REST API search endpoint failed: {str(e)}")

            # If all attempts fail, return empty list
            logger.info(f"📝 Memory retrieval attempted for OpenMemory (agent: {agent_id}, query: {query})")
            return []

        except Exception as e:
            logger.error(f"❌ Error retrieving memories from OpenMemory: {str(e)}")
            return []

# Global instance
openmemory_adapter = OpenMemoryMCPAdapter()
