"""
Local memory management tools for multi-agent systems
"""
import asyncio
from typing import Dict, List, Any, Optional
import json
import os
from datetime import datetime, timedelta
import uuid
from config import Config

class LocalMemoryStore:
    """Simple local file-based memory storage"""

    def __init__(self, storage_path: str):
        self.storage_path = storage_path
        os.makedirs(storage_path, exist_ok=True)

    def _get_file_path(self, agent_id: str) -> str:
        """Get file path for agent memory"""
        return os.path.join(self.storage_path, f"{agent_id}_memory.json")

    def _load_memories(self, agent_id: str) -> List[Dict[str, Any]]:
        """Load memories from file"""
        file_path = self._get_file_path(agent_id)
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"Error loading memories for {agent_id}: {str(e)}")
        return []

    def _save_memories(self, agent_id: str, memories: List[Dict[str, Any]]):
        """Save memories to file"""
        file_path = self._get_file_path(agent_id)
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(memories, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Error saving memories for {agent_id}: {str(e)}")

    def store_memory(self, agent_id: str, content: str, metadata: Dict[str, Any] = None) -> str:
        """Store a memory"""
        memory_id = str(uuid.uuid4())
        memory = {
            "id": memory_id,
            "content": content,
            "metadata": metadata or {},
            "timestamp": datetime.now().isoformat(),
            "agent_id": agent_id
        }

        memories = self._load_memories(agent_id)
        memories.append(memory)

        # Keep only recent memories (limit by config)
        max_memories = Config.MEMORY_CONFIG.get("max_memories_per_agent", 1000)
        if len(memories) > max_memories:
            memories = memories[-max_memories:]

        self._save_memories(agent_id, memories)
        return memory_id

    def retrieve_memories(self, agent_id: str, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Retrieve memories based on query (simple text matching)"""
        memories = self._load_memories(agent_id)
        query_lower = query.lower()

        # Simple relevance scoring based on text matching
        scored_memories = []
        for memory in memories:
            content = memory.get("content", "").lower()
            score = 0

            # Count query word matches
            query_words = query_lower.split()
            for word in query_words:
                if word in content:
                    score += 1

            if score > 0:
                memory["relevance_score"] = score
                scored_memories.append(memory)

        # Sort by relevance and timestamp
        scored_memories.sort(key=lambda x: (x["relevance_score"], x["timestamp"]), reverse=True)

        return scored_memories[:limit]

    def get_all_memories(self, agent_id: str) -> List[Dict[str, Any]]:
        """Get all memories for an agent"""
        return self._load_memories(agent_id)

    def delete_memory(self, agent_id: str, memory_id: str) -> bool:
        """Delete a specific memory"""
        memories = self._load_memories(agent_id)
        original_count = len(memories)
        memories = [m for m in memories if m.get("id") != memory_id]

        if len(memories) < original_count:
            self._save_memories(agent_id, memories)
            return True
        return False

class AgentMemoryManager:
    """Memory manager for individual agents using local storage"""

    def __init__(self, agent_id: str):
        self.agent_id = agent_id
        self.storage = LocalMemoryStore(Config.MEMORY_CONFIG["storage_path"])
        self.session_id = f"agent_{agent_id}_session"

    async def store_memory(self, content: str, metadata: Dict[str, Any] = None) -> str:
        """Store a memory for the agent"""
        try:
            memory_id = self.storage.store_memory(self.agent_id, content, metadata)
            return memory_id
        except Exception as e:
            print(f"Error storing memory for agent {self.agent_id}: {str(e)}")
            return ""

    async def retrieve_memories(self, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        """Retrieve relevant memories for the agent"""
        try:
            results = self.storage.retrieve_memories(self.agent_id, query, limit)
            return results
        except Exception as e:
            print(f"Error retrieving memories for agent {self.agent_id}: {str(e)}")
            return []

    async def get_all_memories(self) -> List[Dict[str, Any]]:
        """Get all memories for the agent"""
        try:
            results = self.storage.get_all_memories(self.agent_id)
            return results
        except Exception as e:
            print(f"Error getting all memories for agent {self.agent_id}: {str(e)}")
            return []

    async def delete_memory(self, memory_id: str) -> bool:
        """Delete a specific memory"""
        try:
            return self.storage.delete_memory(self.agent_id, memory_id)
        except Exception as e:
            print(f"Error deleting memory {memory_id} for agent {self.agent_id}: {str(e)}")
            return False

class SharedMemoryManager:
    """Shared memory manager for cross-agent communication"""

    def __init__(self):
        self.storage = LocalMemoryStore(Config.MEMORY_CONFIG["storage_path"])
        self.shared_agent_id = "shared_agent_memory"

    async def store_shared_memory(
        self,
        content: str,
        source_agent: str,
        target_agents: List[str] = None,
        metadata: Dict[str, Any] = None
    ) -> str:
        """Store shared memory accessible by multiple agents"""
        try:
            enhanced_metadata = {
                "source_agent": source_agent,
                "target_agents": target_agents or [],
                "shared": True,
                **(metadata or {})
            }

            memory_id = self.storage.store_memory(
                self.shared_agent_id,
                content,
                enhanced_metadata
            )
            return memory_id
        except Exception as e:
            print(f"Error storing shared memory: {str(e)}")
            return ""

    async def retrieve_shared_memories(
        self,
        query: str,
        requesting_agent: str,
        limit: int = 5
    ) -> List[Dict[str, Any]]:
        """Retrieve shared memories relevant to the query"""
        try:
            results = self.storage.retrieve_memories(self.shared_agent_id, query, limit)

            # Filter results based on target agents if specified
            filtered_results = []
            for result in results:
                metadata = result.get("metadata", {})
                target_agents = metadata.get("target_agents", [])

                # Include if no target agents specified or requesting agent is in target list
                if not target_agents or requesting_agent in target_agents:
                    filtered_results.append(result)

            return filtered_results
        except Exception as e:
            print(f"Error retrieving shared memories: {str(e)}")
            return []

# Memory tool functions for agents
async def store_agent_memory(agent_id: str, content: str, metadata: Dict[str, Any] = None) -> str:
    """Tool function to store memory for an agent"""
    manager = AgentMemoryManager(agent_id)
    return await manager.store_memory(content, metadata)

async def retrieve_agent_memories(agent_id: str, query: str, limit: int = 5) -> List[Dict[str, Any]]:
    """Tool function to retrieve memories for an agent"""
    manager = AgentMemoryManager(agent_id)
    return await manager.retrieve_memories(query, limit)

async def store_shared_memory(
    content: str,
    source_agent: str,
    target_agents: List[str] = None,
    metadata: Dict[str, Any] = None
) -> str:
    """Tool function to store shared memory"""
    manager = SharedMemoryManager()
    return await manager.store_shared_memory(content, source_agent, target_agents, metadata)

async def retrieve_shared_memories(
    query: str,
    requesting_agent: str,
    limit: int = 5
) -> List[Dict[str, Any]]:
    """Tool function to retrieve shared memories"""
    manager = SharedMemoryManager()
    return await manager.retrieve_shared_memories(query, requesting_agent, limit)
